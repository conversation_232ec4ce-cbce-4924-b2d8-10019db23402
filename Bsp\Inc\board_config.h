/**
  ******************************************************************************
  * @file       : board_config.h
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : 开发板与产品配置文件
  ******************************************************************************
  * @attention
  *
  * 此文件用于配置开发板模式和产品模式的差异
  * 通过定义或注释 DEV_BOARD_MODE 宏来切换
  *
  ******************************************************************************
  */

#ifndef _BOARD_CONFIG_H
#define _BOARD_CONFIG_H

/* 板级配置选择 --------------------------------------------------------------*/
// 开发板模式 - 注释此行切换到产品模式
//#define DEV_BOARD_MODE

/* HSE晶振频率定义 - 必须在包含stm32f1xx_hal_conf.h之前定义 */
#ifdef DEV_BOARD_MODE
    #undef HSE_VALUE
    #define HSE_VALUE               25000000U   // 25MHz晶振
#endif

/* 硬件配置差异 --------------------------------------------------------------*/
#ifdef DEV_BOARD_MODE
    /* 开发板配置 */
    // 晶振频率 - 需要在包含stm32f1xx_hal_conf.h之前定义
    // 在stm32f1xx_hal_conf.h中已有条件编译，这里通过预处理器定义
    
    // CAN引脚配置
    // CAN1: PD0(RX), PD1(TX) - 使用AFIO重映射
    // CAN2: PB5(RX), PB6(TX) - 使用AFIO重映射
    
    // PLL配置（25MHz晶振产生72MHz系统时钟）
    // 使用PLL2方案获得精准72MHz：
    // 25MHz -> PREDIV2(/5) -> 5MHz -> PLL2(*8) -> 40MHz -> PREDIV1(/5) -> 8MHz -> PLL(*9) -> 72MHz
    // 注意：在STM32F107中，当使用PLL2时，HSEPredivValue实际控制的是PREDIV1的分频值
    
#else
    /* 产品配置 */
    // 晶振频率 - 8MHz是默认值，不需要重新定义
    
    // CAN引脚配置
    // CAN1: PA11(RX), PA12(TX) - 默认引脚
    // CAN2: PB12(RX), PB13(TX) - 默认引脚
    
    // PLL配置（8MHz晶振产生72MHz系统时钟）
    // 直接使用PLL：8MHz -> PLL -> 72MHz
    // PLL输入分频：8MHz / 1 = 8MHz
    // PLL倍频：8MHz * 9 = 72MHz
    
#endif

#endif /* _BOARD_CONFIG_H */

/*********************************************END OF FILE**********************/
