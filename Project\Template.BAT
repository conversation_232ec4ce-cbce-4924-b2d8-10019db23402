SET PATH=D:\Work\Keil\ARM\ARMCLANG\Bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Work\VMware Workstation\bin\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\dotnet\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files (x86)\IVI Foundation\VISA\WinNT\Bin\;C:\Program Files\IVI Foundation\VISA\Win64\Bin\;C:\Program Files (x86)\IVI Foundation\IVI\bin;C:\Program Files\IVI Foundation\IVI\bin;C:\Program Files (x86)\IVI Foundation\VISA\WinNT\Bin;D:\Work\Matlab R2021a\runtime\win64;D:\Work\Matlab R2021a\bin;D:\Work\Matlab R2021a\polyspace\bin;D:\Tool\LxRunOffline;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Tool\Microsoft VS Code\bin;D:\Work\QuartusII\modelsim_ase\win32aloem
SET CPU_TYPE=STM32F103VE
SET CPU_VENDOR=STMicroelectronics
SET UV2_TARGET=Template
SET CPU_CLOCK=0x007A1200
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\startup_stm32f103xe._ac"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\system_stm32f1xx.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_adc.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_adc_ex.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_cortex.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_crc.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_dac.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_dac_ex.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_dma.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_exti.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_flash.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_flash_ex.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_gpio.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_gpio_ex.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_pwr.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_rcc.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_rcc_ex.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_tim.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_tim_ex.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_uart.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_hal_usart.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\main.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\stm32f1xx_it.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\delay.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\sys.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\usart.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\adc.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\adf4002.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\gm4912.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\lmx2572.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\lmx2594.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\sw.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmClang.exe" @".\objects\time.__i"
"D:\Work\Keil\ARM\ARMCLANG\Bin\ArmLink" --Via ".\Objects\Template.lnp"
"D:\Work\Keil\ARM\ARMCLANG\Bin\fromelf.exe" ".\Objects\Template.axf" --i32combined --output ".\Objects\Template.hex"
