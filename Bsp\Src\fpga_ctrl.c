/**
  ******************************************************************************
  * @file       : fpga_ctrl.c
  * <AUTHOR> yechen
  * @version     : V1.0.0
  * @brief       : FPGA控制接口实现
  ******************************************************************************
  * @attention
  *
  * 通过SPI1接口与FPGA通信，实现频综模块参数控制
  * SPI1引脚: PA4(NSS) PA5(SCK) PA6(MISO) PA7(MOSI)
  * 协议：大端模式，CKPOL=0, CKPHA=0，MSB优先
  *
  ******************************************************************************
  */

/* 头文件包含 ----------------------------------------------------------------*/
#include "fpga_ctrl.h"
#include "spi.h"
#include "sys.h"
#include "CanDataProcess.h"

/* 外部变量声明 --------------------------------------------------------------*/
extern SPI_HandleTypeDef hspi1;
extern ModuleParams_t moduleParams;

/* 私有变量定义 --------------------------------------------------------------*/
static uint8_t fpgaReadBuffer[43];  // FPGA读取响应缓冲区

/**
  * @brief  低段一本振频率转换
  * @param  canFreq -- CAN接收的频率 (kHz) [30000-3000000]
  * @retval 转换后的频率 (kHz) [4000000-6970000]
  */
static uint32_t ConvertLowFrequency(uint32_t canFreq)
{
    // CAN 30M对应频综4000M，3000M对应6970M
    // 线性转换：y = k*x + b
    // k = (6970000 - 4000000) / (3000000 - 30000) = 2970000 / 2970000 = 1
    // b = 4000000 - 1 * 30000 = 3970000
    return canFreq + 3970000;
}

/**
  * @brief  SPI数据帧发送
  * @param  txData -- 发送数据
  * @param  rxData -- 接收数据
  * @param  length -- 数据长度
  * @retval 无
  */
static void FPGA_SendFrame(uint8_t *txData, uint8_t *rxData, uint8_t length)
{
    // 拉低CS开始传输
    SPI1_NSS = 0;

    // 发送数据并接收响应
    for (uint8_t i = 0; i < length; i++) {
        // 等待发送缓冲区空
        while (!(hspi1.Instance->SR & SPI_SR_TXE));
        hspi1.Instance->DR = txData[i];

        // 等待接收缓冲区非空
        while (!(hspi1.Instance->SR & SPI_SR_RXNE));
        rxData[i] = hspi1.Instance->DR;
    }

    // 等待传输完成
    while (hspi1.Instance->SR & SPI_SR_BSY);

    // 拉高CS结束传输
    SPI1_NSS = 1;
}

/**
  * @brief  发送参数设置命令到FPGA
  * @param  channelNum -- 信道编号 (0x01-0x09)
  * @param  workMode -- 工作模式
  * @param  lowFreq -- 低段一本振频率 (kHz)
  * @param  high1Freq -- 高段一本振频率 (kHz)
  * @param  high2Freq -- 高段二本振频率 (kHz)
  * @retval 无
  */
void FPGA_SetChannelParams(uint8_t channelNum, uint8_t workMode, uint32_t lowFreq, uint32_t high1Freq, uint32_t high2Freq)
{
    uint8_t txBuffer[18];
    uint8_t rxBuffer[18];
    uint32_t convertedLowFreq;

    // 构建SPI帧
    txBuffer[0] = 0xF8;  // 帧头
    txBuffer[1] = 0x00;  // 写操作

    // BYTE2~3: 开关控制 - 每次下发通道参数设置时都给1（开电状态）
    txBuffer[2] = 0xFF;  // 低8位全部开电
    txBuffer[3] = 0x03;  // 高2位开电，其余保留

    // BYTE4: 信道编号
    txBuffer[4] = channelNum;

    // BYTE5: 工作模式
    txBuffer[5] = workMode & 0x03;

    // BYTE6~8: 低段一本振中心频率控制（需要转换）
    convertedLowFreq = ConvertLowFrequency(lowFreq);
    txBuffer[6] = (convertedLowFreq >> 16) & 0xFF;  // 高字节
    txBuffer[7] = (convertedLowFreq >> 8) & 0xFF;   // 中字节
    txBuffer[8] = convertedLowFreq & 0xFF;          // 低字节

    // BYTE9~12: 高段一本振中心频率控制（直接透传）
    txBuffer[9] = (high1Freq >> 24) & 0xFF;
    txBuffer[10] = (high1Freq >> 16) & 0xFF;
    txBuffer[11] = (high1Freq >> 8) & 0xFF;
    txBuffer[12] = high1Freq & 0xFF;

    // BYTE13~16: 高段二本振中心频率控制（直接透传）
    txBuffer[13] = (high2Freq >> 24) & 0xFF;
    txBuffer[14] = (high2Freq >> 16) & 0xFF;
    txBuffer[15] = (high2Freq >> 8) & 0xFF;
    txBuffer[16] = high2Freq & 0xFF;

    // BYTE17: 累加校验（内部校准用）
    txBuffer[17] = 0x00;

    // 发送SPI帧
    FPGA_SendFrame(txBuffer, rxBuffer, 18);
}

/**
  * @brief  读取FPGA参数
  * @param  无
  * @retval 无
  */
void FPGA_ReadParams(void)
{
    uint8_t txBuffer[43];

    // 构建读取命令帧
    txBuffer[0] = 0xF8;  // 帧头
    txBuffer[1] = 0x01;  // 读操作

    // 其余字节填充0（读操作时不需要数据）
    for (uint8_t i = 2; i < 43; i++) {
        txBuffer[i] = 0x00;
    }

    // 发送读取命令并接收响应
    FPGA_SendFrame(txBuffer, fpgaReadBuffer, 43);
}

/**
  * @brief  获取温度值
  * @retval 温度值 (°C)
  */
int8_t FPGA_GetTemperature(void)
{
    // 从读取缓冲区获取温度值（BYTE2）
    return (int8_t)fpgaReadBuffer[2];
}

/**
  * @brief  获取锁定状态
  * @retval 锁定状态位
  */
uint16_t FPGA_GetLockStatus(void)
{
    // 从读取缓冲区获取锁定状态（BYTE3~4）
    return (fpgaReadBuffer[3] | (fpgaReadBuffer[4] << 8));
}

/**
  * @brief  处理CAN通道参数设置
  * @param  channelNum -- 通道编号
  * @param  workMode -- 工作模式
  * @param  lowFreq -- 低段频率
  * @param  high1Freq -- 高段一本振频率
  * @param  high2Freq -- 高段二本振频率
  * @retval 无
  */
void FPGA_ProcessChannelSet(uint8_t channelNum, uint8_t workMode, uint32_t lowFreq, uint32_t high1Freq, uint32_t high2Freq)
{
    switch (channelNum) {
        case 0x01:  // 低段一本振1通道参数设置
            FPGA_SetChannelParams(channelNum, workMode, lowFreq, 0, 0);
            break;

        case 0x02:  // 低段一本振2通道参数设置
            FPGA_SetChannelParams(channelNum, workMode, lowFreq, 0, 0);
            break;

        case 0x03:  // 低段一本振开关/功分切换参数设置
            FPGA_SetChannelParams(channelNum, workMode, 0, 0, 0);
            break;

        case 0x04:  // 高段一本振1通道参数设置
            FPGA_SetChannelParams(channelNum, workMode, 0, high1Freq, 0);
            break;

        case 0x05:  // 高段二本振1通道参数设置
            FPGA_SetChannelParams(channelNum, workMode, 0, 0, high2Freq);
            break;

        case 0x06:  // 高段一本振2通道参数设置
            FPGA_SetChannelParams(channelNum, workMode, 0, high1Freq, 0);
            break;

        case 0x07:  // 高段二本振2通道参数设置
            FPGA_SetChannelParams(channelNum, workMode, 0, 0, high2Freq);
            break;

        case 0x08:  // 高段一本振3通道参数设置
            FPGA_SetChannelParams(channelNum, workMode, 0, high1Freq, 0);
            break;

        case 0x09:  // 高段二本振3通道参数设置
            FPGA_SetChannelParams(channelNum, workMode, 0, 0, high2Freq);
            break;

        case 0x00:  // 通道同时设置
            FPGA_SetChannelParams(channelNum, workMode, lowFreq, high1Freq, high2Freq);
            break;

        default:
            // 无效通道号，不处理
            break;
    }
}

/*********************************************END OF FILE**********************/