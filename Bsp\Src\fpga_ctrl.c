/**
  ******************************************************************************
  * @file       : fpga_ctrl.c
  * <AUTHOR> yechen
  * @version     : V1.0.0
  * @brief       : FPGA控制接口实现
  ******************************************************************************
  * @attention
  *
  * 通过SPI1接口与FPGA通信，实现通道参数控制
  * SPI1引脚: PA4(NSS) PA5(SCK) PA6(MISO) PA7(MOSI)
  *
  ******************************************************************************
  */
  
/* 头文件包含 ----------------------------------------------------------------*/
#include "fpga_ctrl.h"
#include "spi.h"
#include "delay.h"
#include "CanDataProcess.h"

/* 外部变量声明 --------------------------------------------------------------*/
extern SPI_HandleTypeDef hspi3;
extern ChannelParams_t channelGroupParams[2];

/* 私有变量定义 --------------------------------------------------------------*/
static uint8_t lastResponse[5];  // 保存上一次的响应

/**
  * @brief  发送命令到FPGA
  * @param  channel -- 通道号 (0x00:全通道, 0x01:通道1-5, 0x02:通道6-10)
  * @param  function -- 功能码
  * @param  data -- 数据
  * @retval 无
  */
void FPGA_SendCommand(uint8_t channel, uint8_t function, uint8_t data)
{
    uint8_t txBuffer[10];
    uint8_t rxBuffer[10];
    
    // 构建命令帧
    txBuffer[0] = 0xAA;         // 帧头
    txBuffer[1] = channel;      // 通道号
    txBuffer[2] = function;     // 功能码
    txBuffer[3] = data;         // 数据
    txBuffer[4] = 0x55;         // 帧尾
    
    // 填充后5字节为0xFF
    for (int i = 5; i < 10; i++) {
        txBuffer[i] = 0xFF;
    }
    
    // 通过SPI3发送命令
    SPI3_NSS = 0;  // CS低电平
    
    // 优化的SPI传输：使用错位读取方式避免等待RXNE
    volatile uint8_t dummy;
    
    // 清除可能的接收缓冲区数据
    dummy = hspi3.Instance->DR;
    
    // 发送第一个字节
    while (!(hspi3.Instance->SR & SPI_SR_TXE));
    hspi3.Instance->DR = txBuffer[0];
    
    // 发送剩余字节，同时读取前一个字节的响应
    for (int i = 1; i < 10; i++) {
        // 等待发送缓冲区空
        while (!(hspi3.Instance->SR & SPI_SR_TXE));
        // 发送下一个字节
        hspi3.Instance->DR = txBuffer[i];
        // 读取前一个字节的接收数据（此时RXNE应该已经置位）
        rxBuffer[i-1] = hspi3.Instance->DR;
    }
    
    // 等待最后一个字节传输完成
    while (hspi3.Instance->SR & SPI_SR_BSY);
    // 读取最后一个字节
    rxBuffer[9] = hspi3.Instance->DR;
    
    // 保存本次命令的响应（在后5字节）
    for (int i = 0; i < 5; i++) {
        lastResponse[i] = rxBuffer[5 + i];
    }
    
    SPI3_NSS = 1;  // CS高电平
}

/**
  * @brief  等待FPGA响应
  * @param  channel -- 期望的通道号
  * @param  function -- 期望的功能码
  * @retval 状态码 (0x01:正常, 0x02:错误)
  */
uint8_t FPGA_WaitResponse(uint8_t channel, uint8_t function)
{
    // 检查响应帧格式
    if (lastResponse[0] == 0xAA &&           // 帧头
        lastResponse[1] == channel &&        // 通道号匹配
        lastResponse[2] == function &&       // 功能码匹配
        lastResponse[4] == 0x55) {           // 帧尾
        return lastResponse[3];               // 返回状态码
    }
    
    return 0x02;  // 错误
}

/**
  * @brief  设置带宽
  * @param  channel -- 通道号
  * @param  bandwidth -- 带宽值 (0:200kHz, 1:5MHz, 2:50MHz)
  * @retval 无
  */
void FPGA_SetBandwidth(uint8_t channel, uint8_t bandwidth)
{
    uint8_t data = 0;
    uint8_t filterV1 = 0;
    uint8_t filterV2 = 0;
    uint8_t filterEN = 0;
    
    // 根据真值表设置带宽滤波器控制位
    // Data[2]=Module_Switch_filterEN (1:直通, 0:滤波)
    // Data[1]=Module_Switch_filterV2
    // Data[0]=Module_Switch_filterV1
    // V1=1, V2=0: 200kHz
    // V1=1, V2=1: 5MHz
    // V1=0, V2=1: 50MHz
    switch (bandwidth) {
        case BANDWIDTH_200KHZ:  // 200kHz
            filterV1 = 1;
            filterV2 = 0;
            filterEN = 0;  // 使用滤波器
            break;
        case BANDWIDTH_5MHZ:    // 5MHz
            filterV1 = 1;
            filterV2 = 1;
            filterEN = 0;  // 使用滤波器
            break;
        case BANDWIDTH_50MHZ:   // 50MHz
            filterV1 = 0;
            filterV2 = 1;
            filterEN = 0;  // 使用滤波器
            break;
        default:
            // 默认50MHz
            filterV1 = 0;
            filterV2 = 1;
            filterEN = 0;
            break;
    }
    
    // 组合控制字
    data = (filterEN << 2) | (filterV2 << 1) | filterV1;
    
    FPGA_SendCommand(channel, FPGA_FUNC_BANDWIDTH, data);
}

/**
  * @brief  设置工作模式
  * @param  channel -- 通道号
  * @param  mode -- 模式 (0:普通工作模式, 1:低功耗工作模式)
  * @retval 无
  */
void FPGA_SetWorkMode(uint8_t channel, uint8_t mode)
{
    // CAN协议: 0=普通工作模式, 1=低功耗工作模式
    // FPGA TD位: 1=电源开启(普通模式), 0=电源关闭(低功耗模式)
    // 所以需要反转逻辑
    uint8_t data = (mode == 0) ? 1 : 0;  // TD位控制电源
    FPGA_SendCommand(channel, FPGA_FUNC_WORKMODE, data);
}

/**
  * @brief  设置射频衰减
  * @param  channel -- 通道号
  * @param  atten -- 衰减值 (0-7, 对应0-35dB, 5dB步进)
  * @retval 无
  */
void FPGA_SetRfAtten(uint8_t channel, uint8_t atten)
{
    uint8_t data = 0;
    
    // 将衰减值转换为控制位
    // Data[2]=Module_RF_ATT20 (20dB)
    // Data[1]=Module_RF_ATT10 (10dB)
    // Data[0]=Module_RF_ATT5  (5dB)
    if (atten & 0x04) {
        data |= 0x04;  // 20dB
    }
    if (atten & 0x02) {
        data |= 0x02;  // 10dB
    }
    if (atten & 0x01) {
        data |= 0x01;  // 5dB
    }
    
    FPGA_SendCommand(channel, FPGA_FUNC_RF_ATTEN, data);
}

/**
  * @brief  设置中频衰减
  * @param  channel -- 通道号
  * @param  atten -- 衰减值 (0-30dB, 1dB步进)
  * @retval 无
  */
void FPGA_SetIfAtten(uint8_t channel, uint8_t atten)
{
    uint8_t data = 0;
    uint8_t temp = atten;
    
    // 将衰减值转换为控制位
    // Data[5]=Module_IF_ATT16   (16dB)
    // Data[4]=Module_IF_ATT8    (8dB)
    // Data[3]=Module_IF_ATT4    (4dB)
    // Data[2]=Module_IF_ATT2    (2dB)
    // Data[1]=Module_IF_ATT1    (1dB)
    // Data[0]=Module_IF_ATT0_5  (0.5dB)
    
    if (temp >= 16) {
        data |= 0x20;  // 16dB
        temp -= 16;
    }
    if (temp >= 8) {
        data |= 0x10;  // 8dB
        temp -= 8;
    }
    if (temp >= 4) {
        data |= 0x08;  // 4dB
        temp -= 4;
    }
    if (temp >= 2) {
        data |= 0x04;  // 2dB
        temp -= 2;
    }
    if (temp >= 1) {
        data |= 0x02;  // 1dB
        temp -= 1;
    }
    // 注意：0.5dB控制位暂不使用
    
    FPGA_SendCommand(channel, FPGA_FUNC_IF_ATTEN, data);
}

/**
  * @brief  设置校正衰减
  * @param  channel -- 通道号
  * @param  atten -- 衰减值 (0-31.5dB, 0.5dB步进)
  * @retval 无
  */
void FPGA_SetCalAtten(uint8_t channel, uint8_t atten)
{
    uint8_t data = 0;
    uint8_t temp = atten * 2;  // 转换为0.5dB单位
    
    // 将衰减值转换为控制位
    // Data[5]=Module_CalATT16   (16dB)
    // Data[4]=Module_CalATT8    (8dB)
    // Data[3]=Module_CalATT4    (4dB)
    // Data[2]=Module_CalATT2    (2dB)
    // Data[1]=Module_CalATT1    (1dB)
    // Data[0]=Module_CalATT0_5  (0.5dB)
    
    if (temp >= 32) {
        data |= 0x20;  // 16dB
        temp -= 32;
    }
    if (temp >= 16) {
        data |= 0x10;  // 8dB
        temp -= 16;
    }
    if (temp >= 8) {
        data |= 0x08;  // 4dB
        temp -= 8;
    }
    if (temp >= 4) {
        data |= 0x04;  // 2dB
        temp -= 4;
    }
    if (temp >= 2) {
        data |= 0x02;  // 1dB
        temp -= 2;
    }
    if (temp >= 1) {
        data |= 0x01;  // 0.5dB
    }
    
    FPGA_SendCommand(channel, FPGA_FUNC_CAL_ATTEN, data);
}

/**
  * @brief  根据频率设置射频开关和滤波器
  * @param  channel -- 通道号
  * @param  frequency -- 频率 (kHz)
  * @retval 无
  */
void FPGA_SetFrequencyBand(uint8_t channel, uint32_t frequency)
{
    uint8_t data = 0;
    uint8_t switchCtrl1 = 0;
    uint8_t switchCtrl2 = 0;
    uint8_t filterBits = 0;
    
    // 根据频率范围设置滤波器和开关
    // Data[6]=Module_switch_ctrl2
    // Data[5]=Module_switch_ctrl1
    // Data[4]=Module_SecFilterV5
    // Data[3]=Module_SecFilterV4
    // Data[2]=Module_SecFilterV3
    // Data[1]=Module_SecFilterV2
    // Data[0]=Module_SecFilterV1
    
    // 根据真值表设置频段滤波器
    // 注意：根据真值表，开关控制switchCtrl1对应表中的"开关控制"
    // 0: 0.03GHz~0.2GHz直通路径
    // 1: 分段滤波器路径
    if (frequency < 200000) {           // < 200MHz (30MHz-200MHz)
        // 开关控制：0 - 选择0.03GHz~0.2GHz路径
        switchCtrl1 = 0;
        switchCtrl2 = 0;
        filterBits = 0x00;  // 不使用分段滤波器
    } 
    else if (frequency < 300000) {      // 200MHz-300MHz
        // 开关控制：1 - 选择分段滤波器路径
        switchCtrl1 = 1;
        switchCtrl2 = 0;
        // V1=0, V2=1, V3=0, V4=0, V5=1
        filterBits = 0x12;  // 0b10010
    } 
    else if (frequency < 450000) {      // 300MHz-450MHz
        switchCtrl1 = 1;
        switchCtrl2 = 0;
        // V1=1, V2=0, V3=0, V4=1, V5=0
        filterBits = 0x09;  // 0b01001
    }
    else if (frequency < 700000) {      // 450MHz-700MHz
        switchCtrl1 = 1;
        switchCtrl2 = 0;
        // V1=0, V2=1, V3=1, V4=0, V5=0
        filterBits = 0x06;  // 0b00110
    }
    else if (frequency < 1100000) {     // 700MHz-1.1GHz
        switchCtrl1 = 1;
        switchCtrl2 = 0;
        // V1=1, V2=0, V3=0, V4=0, V5=1
        filterBits = 0x11;  // 0b10001
    }
    else if (frequency < 1800000) {     // 1.1GHz-1.8GHz
        switchCtrl1 = 1;
        switchCtrl2 = 0;
        // V1=0, V2=1, V3=0, V4=1, V5=0
        filterBits = 0x0A;  // 0b01010
    }
    else {                              // 1.8GHz-3GHz
        switchCtrl1 = 1;
        switchCtrl2 = 0;
        // V1=1, V2=0, V3=1, V4=0, V5=0
        filterBits = 0x05;  // 0b00101
    }
    
    // 组合控制字
    data = (switchCtrl2 << 6) | (switchCtrl1 << 5) | filterBits;
    
    FPGA_SendCommand(channel, FPGA_FUNC_RF_SWITCH, data);
}

/**
  * @brief  批量发送命令（优化版）
  * @param  commands -- 命令数组，每个命令包含 {channel, function, data}
  * @param  count -- 命令数量
  * @retval 无
  */
void FPGA_SendBatchCommands(uint8_t commands[][3], uint8_t count)
{
    uint8_t txBuffer[10];
    uint8_t rxBuffer[10]; 
    volatile uint8_t dummy;
    
    // 设置不变的帧头帧尾
    txBuffer[0] = 0xAA;
    txBuffer[4] = 0x55;
    for (int i = 5; i < 10; i++) {
        txBuffer[i] = 0xFF;
    }
    
    // 拉低CS，开始批量传输
    SPI3_NSS = 0;
    
    // 清除可能的接收缓冲区数据
    dummy = hspi3.Instance->DR;
    
    // 批量发送所有命令
    for (int cmd = 0; cmd < count; cmd++) {
        // 更新命令内容
        txBuffer[1] = commands[cmd][0];  // channel
        txBuffer[2] = commands[cmd][1];  // function  
        txBuffer[3] = commands[cmd][2];  // data
        
        // 发送第一个字节
        while (!(hspi3.Instance->SR & SPI_SR_TXE));
        hspi3.Instance->DR = txBuffer[0];
        
        // 发送剩余字节，同时读取前一个字节的响应
        for (int i = 1; i < 10; i++) {
            while (!(hspi3.Instance->SR & SPI_SR_TXE));
            hspi3.Instance->DR = txBuffer[i];
            rxBuffer[i-1] = hspi3.Instance->DR;
        }
        
        // 读取最后一个字节
        while (!(hspi3.Instance->SR & SPI_SR_RXNE));
        rxBuffer[9] = hspi3.Instance->DR;
        
        // 如果是最后一个命令，保存响应
        if (cmd == count - 1) {
            for (int i = 0; i < 5; i++) {
                lastResponse[i] = rxBuffer[5 + i];
            }
        }
    }
    
    // 等待最后传输完成
    while (hspi3.Instance->SR & SPI_SR_BSY);
    
    // 拉高CS
    SPI3_NSS = 1;
}

/**
  * @brief  更新通道参数到FPGA
  * @param  channel -- 通道号 (0x00:全通道, 0x01:通道1-5, 0x02:通道6-10)
  * @retval 无
  */
void FPGA_UpdateChannelParams(uint8_t channel)
{
    int groupIdx = 0;
    uint8_t commands[5][3];  // 最多5个命令
    
    if (channel == FPGA_CHANNEL_ALL) {
        // 全通道更新 - 使用两组参数的相同值（假设全通道设置时两组参数相同）
        // 使用第0组参数应用到全通道
        groupIdx = 0;
    } else if (channel == FPGA_CHANNEL_1TO5) {
        groupIdx = 0;  // 通道1-5使用第0组参数
    } else if (channel == FPGA_CHANNEL_6TO10) {
        groupIdx = 1;  // 通道6-10使用第1组参数
    } else {
        return;  // 无效通道
    }
    
    // 准备批量命令
    // 命令1：工作模式
    commands[0][0] = channel;
    commands[0][1] = FPGA_FUNC_WORKMODE;
    commands[0][2] = (channelGroupParams[groupIdx].workMode == 0) ? 1 : 0;
    
    // 命令2：带宽
    commands[1][0] = channel;
    commands[1][1] = FPGA_FUNC_BANDWIDTH;
    uint8_t filterV1 = 0, filterV2 = 0, filterEN = 0;
    switch (channelGroupParams[groupIdx].bandwidth) {
        case BANDWIDTH_200KHZ:
            filterV1 = 1; filterV2 = 0; filterEN = 0;
            break;
        case BANDWIDTH_5MHZ:
            filterV1 = 1; filterV2 = 1; filterEN = 0;
            break;
        case BANDWIDTH_50MHZ:
            filterV1 = 0; filterV2 = 1; filterEN = 0;
            break;
    }
    commands[1][2] = (filterEN << 2) | (filterV2 << 1) | filterV1;
    
    // 命令3：频率开关
    commands[2][0] = channel;
    commands[2][1] = FPGA_FUNC_RF_SWITCH;
    // 简化频率判断逻辑，内联计算
    uint32_t freq = channelGroupParams[groupIdx].frequency;
    uint8_t switchCtrl1 = (freq >= 200000) ? 1 : 0;
    uint8_t filterBits = 0;
    if (freq < 200000) {
        filterBits = 0x00;
    } else if (freq < 300000) {
        filterBits = 0x12;
    } else if (freq < 450000) {
        filterBits = 0x09;
    } else if (freq < 700000) {
        filterBits = 0x06;
    } else if (freq < 1100000) {
        filterBits = 0x11;
    } else if (freq < 1800000) {
        filterBits = 0x0A;
    } else {
        filterBits = 0x05;
    }
    commands[2][2] = (switchCtrl1 << 5) | filterBits;
    
    // 命令4：射频衰减
    commands[3][0] = channel;
    commands[3][1] = FPGA_FUNC_RF_ATTEN;
    commands[3][2] = channelGroupParams[groupIdx].rfAtten & 0x07;
    
    // 命令5：中频衰减
    commands[4][0] = channel;
    commands[4][1] = FPGA_FUNC_IF_ATTEN;
    uint8_t ifData = 0;
    uint8_t temp = channelGroupParams[groupIdx].ifAtten;
    if (temp >= 16) { ifData |= 0x20; temp -= 16; }
    if (temp >= 8)  { ifData |= 0x10; temp -= 8; }
    if (temp >= 4)  { ifData |= 0x08; temp -= 4; }
    if (temp >= 2)  { ifData |= 0x04; temp -= 2; }
    if (temp >= 1)  { ifData |= 0x02; }
    commands[4][2] = ifData;
    
    // 批量发送所有命令
    FPGA_SendBatchCommands(commands, 5);
}

/*********************************************END OF FILE**********************/
