/**
  ******************************************************************************
  * @file       : CanDataProcess.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : CAN总线数据处理实现
  ******************************************************************************
  * @attention
  *
  * 根据通信协议文档实现CAN数据解析和响应
  *
  ******************************************************************************
  */
  
/* 头文件包含 ----------------------------------------------------------------*/
#include "CanDataProcess.h"
#include "STM32Flash.h"
#include "delay.h"
#include "can.h"
#include "spi.h"
#include "adc.h"
#include "fpga_ctrl.h"

/* 变量声明 -------------------------------------------------------------------*/
extern CAN_IdTypeDef CanIdConfig;	// CAN发送ID 

/* 变量定义 -------------------------------------------------------------------*/
// 2组通道的参数 [0]:通道1-5, [1]:通道6-10
ChannelParams_t channelGroupParams[2] = {0};

// 产品信息
uint8_t manufacturerCode = 0x00;   // 厂家代号: 0x00表示十所
uint16_t productYear = 0x2025;     // 出厂年份 (BCD码)
uint8_t productMonth = 0x01;       // 出厂月份 (BCD码)
uint8_t productDay = 0x01;         // 出厂日期 (BCD码)
uint16_t serialYear = 0x2025;      // 序列号年份 (BCD码)
uint8_t serialBatch = 0x01;        // 序列号批次 (BCD码)
uint16_t serialNum = 0x001;        // 序列号序号 (BCD码)

/**
  * @brief  解析CAN接收数据中的参数
  * @param  CanRxData -- 接收数组
  * @param  params -- 输出参数结构体
  * @retval 无
  */
static void ParseCanRxData(uint8_t CanRxData[], CanRxParams_t *params)
{
    params->msgCategory = CanRxData[0];
    params->msgId = CanRxData[1];
    
    switch (params->msgId)
    {
        case 0x02:  // 通道参数查询
        case 0x04:  // 通道参数设置
            params->channelNum = CanRxData[2] & 0x0F;
            if (params->msgId == 0x04) {
                params->bandwidthValid = (CanRxData[3] >> 4) & 0x0F;
                params->bandwidth = (CanRxData[3] >> 1) & 0x07;
                params->workMode = CanRxData[3] & 0x01;
                params->frequency = (CanRxData[4] << 24) | (CanRxData[5] << 16) | 
                                  (CanRxData[6] << 8) | CanRxData[7];
                params->rfAtten = CanRxData[8];
                params->ifAtten = CanRxData[9];
            }
            break;
    }
}

/**
  * @brief  CAN接收数据处理
  * @param  CanRxData -- 接收数组  
  * @param  CanDataLength -- 数据长度
  * @param  MarkAddr -- MARK地址
  * @retval 无
  */
void CanRxDataProcess(uint8_t CanRxData[], uint16_t CanDataLength, uint8_t MarkAddr)
{
    uint8_t CanSendData[256];
    uint16_t sendLen = 0;
    uint8_t i, j;
    CanRxParams_t rxParams = {0};
    uint32_t productInfo[3];
    static uint8_t productInfoLoaded = 0;
    
    // 第一次调用时从Flash读取产品信息
    if (!productInfoLoaded) {
        Stm32Flash_Read(STORE_ADDR, productInfo, 3);
        if (productInfo[0] != 0xFFFFFFFF) {  // Flash非空
            productYear = (productInfo[0] >> 16) & 0xFFFF;
            productMonth = (productInfo[0] >> 8) & 0xFF;
            productDay = productInfo[0] & 0xFF;
            serialYear = (productInfo[1] >> 16) & 0xFFFF;
            serialBatch = (productInfo[1] >> 8) & 0xFF;
            serialNum = ((productInfo[1] & 0xFF) << 8) | ((productInfo[2] >> 8) & 0xFF);
        }
        productInfoLoaded = 1;
    }
    
    // 解析接收数据
    ParseCanRxData(CanRxData, &rxParams);
    
    // 根据消息类别和ID进行处理
    if (rxParams.msgCategory == 0xF0)
    {
        switch (rxParams.msgId)
        {
            case 0x00:  // 模块产品信息查询
                CanSendData[0] = 0xF0;
                CanSendData[1] = 0x00;
                CanSendData[2] = manufacturerCode;                    // 厂家代号
                CanSendData[3] = (productYear >> 8) & 0xFF;          // 出厂时间-年 千位和百位BCD
                CanSendData[4] = productYear & 0xFF;                 // 出厂时间-年 十位和个位BCD
                CanSendData[5] = productMonth;                        // 出厂时间-月 BCD
                CanSendData[6] = productDay;                          // 出厂时间-日 BCD
                CanSendData[7] = (serialYear >> 8) & 0xFF;           // 序列号-年 千位和百位BCD
                CanSendData[8] = serialYear & 0xFF;                  // 序列号-年 十位和个位BCD
                CanSendData[9] = serialBatch;                         // 序列号-批次 BCD
                CanSendData[10] = (serialNum >> 8) & 0xFF;           // 序列号-保留4位 + 序号百位BCD
                CanSendData[11] = serialNum & 0xFF;                  // 序列号-序号 十位和个位BCD
                CanSendData[12] = MarkAddr;                           // MARK地址
                // 工作频率下限 30000 kHz (0x00007530)
                CanSendData[13] = 0x00;
                CanSendData[14] = 0x00;
                CanSendData[15] = 0x75;
                CanSendData[16] = 0x30;
                // 工作频率上限 3000000 kHz (0x002DC6C0)
                CanSendData[17] = 0x00;
                CanSendData[18] = 0x2D;
                CanSendData[19] = 0xC6;
                CanSendData[20] = 0xC0;
                
                CANxSend(CanSendData, 21, 0x04, MarkAddr);
                break;
                
            case 0x01:  // 模块BIT查询
                CanSendData[0] = 0xF0;
                CanSendData[1] = 0x01;
                CanSendData[2] = 0;  // 保留字节
                CanSendData[3] = GetTempValeue();  // 当前温度检测值
                
                // 10个通道的+5.5V检测电压
                for (i = 0; i < 10; i++) {
                    CanSendData[4 + i] = GetVoltage5_5V();  // 获取电压值
                }
                
                CANxSend(CanSendData, 14, 0x04, MarkAddr);
                break;
                
            case 0x02:  // 模块通道参数查询
                CanSendData[0] = 0xF0;
                CanSendData[1] = 0x02;
                CanSendData[2] = rxParams.channelNum;
                
                if (rxParams.channelNum == 0x00) {
                    // 全通道查询 - 返回2组参数
                    // 通道1-5组参数
                    j = 3;
                    CanSendData[j] = channelGroupParams[0].rfAtten;
                    CanSendData[j+1] = channelGroupParams[0].ifAtten;
                    CanSendData[j+2] = (channelGroupParams[0].frequency >> 24) & 0xFF;
                    CanSendData[j+3] = (channelGroupParams[0].frequency >> 16) & 0xFF;
                    CanSendData[j+4] = (channelGroupParams[0].frequency >> 8) & 0xFF;
                    CanSendData[j+5] = channelGroupParams[0].frequency & 0xFF;
                    CanSendData[j+6] = (channelGroupParams[0].bandwidth << 1) | 
                                      channelGroupParams[0].workMode;
                    
                    // 通道6-10组参数
                    j = 10;
                    CanSendData[j] = channelGroupParams[1].rfAtten;
                    CanSendData[j+1] = channelGroupParams[1].ifAtten;
                    CanSendData[j+2] = (channelGroupParams[1].frequency >> 24) & 0xFF;
                    CanSendData[j+3] = (channelGroupParams[1].frequency >> 16) & 0xFF;
                    CanSendData[j+4] = (channelGroupParams[1].frequency >> 8) & 0xFF;
                    CanSendData[j+5] = channelGroupParams[1].frequency & 0xFF;
                    CanSendData[j+6] = (channelGroupParams[1].bandwidth << 1) | 
                                      channelGroupParams[1].workMode;
                    sendLen = 17;
                }
                else if (rxParams.channelNum == 0x01) {
                    // 通道1-5参数查询
                    j = 3;
                    CanSendData[j] = channelGroupParams[0].rfAtten;
                    CanSendData[j+1] = channelGroupParams[0].ifAtten;
                    CanSendData[j+2] = (channelGroupParams[0].frequency >> 24) & 0xFF;
                    CanSendData[j+3] = (channelGroupParams[0].frequency >> 16) & 0xFF;
                    CanSendData[j+4] = (channelGroupParams[0].frequency >> 8) & 0xFF;
                    CanSendData[j+5] = channelGroupParams[0].frequency & 0xFF;
                    CanSendData[j+6] = (channelGroupParams[0].bandwidth << 1) | 
                                      channelGroupParams[0].workMode;
                    sendLen = 10;
                }
                else if (rxParams.channelNum == 0x02) {
                    // 通道6-10参数查询
                    j = 3;
                    CanSendData[j] = channelGroupParams[1].rfAtten;
                    CanSendData[j+1] = channelGroupParams[1].ifAtten;
                    CanSendData[j+2] = (channelGroupParams[1].frequency >> 24) & 0xFF;
                    CanSendData[j+3] = (channelGroupParams[1].frequency >> 16) & 0xFF;
                    CanSendData[j+4] = (channelGroupParams[1].frequency >> 8) & 0xFF;
                    CanSendData[j+5] = channelGroupParams[1].frequency & 0xFF;
                    CanSendData[j+6] = (channelGroupParams[1].bandwidth << 1) | 
                                      channelGroupParams[1].workMode;
                    sendLen = 10;
                }
                
                CANxSend(CanSendData, sendLen, 0x04, MarkAddr);
                break;
                
            case 0x04:  // 通道参数设置
                // 只处理有效的通道号：0x00, 0x01, 0x02
                if (rxParams.channelNum > 0x02) {
                    // 忽略无效通道号（包括单通道控制）
                    break;
                }
                
                // 更新通道参数
                if (rxParams.channelNum == 0x00) {
                    // 全通道设置 - 更新2组参数
                    for (i = 0; i < 2; i++) {
                        if (rxParams.bandwidthValid) {
                            channelGroupParams[i].bandwidth = rxParams.bandwidth;
                        }
                        channelGroupParams[i].workMode = rxParams.workMode;
                        channelGroupParams[i].frequency = rxParams.frequency;
                        channelGroupParams[i].rfAtten = rxParams.rfAtten;
                        channelGroupParams[i].ifAtten = rxParams.ifAtten;
                    }
                    // 更新FPGA参数，直接使用rxParams.channelNum
                    FPGA_UpdateChannelParams(rxParams.channelNum);
                }
                else if (rxParams.channelNum == 0x01) {
                    // 通道1-5设置（组控制）
                    if (rxParams.bandwidthValid) {
                        channelGroupParams[0].bandwidth = rxParams.bandwidth;
                    }
                    channelGroupParams[0].workMode = rxParams.workMode;
                    channelGroupParams[0].frequency = rxParams.frequency;
                    channelGroupParams[0].rfAtten = rxParams.rfAtten;
                    channelGroupParams[0].ifAtten = rxParams.ifAtten;
                    
                    // 更新FPGA参数，直接使用rxParams.channelNum
                    FPGA_UpdateChannelParams(rxParams.channelNum);
                }
                else if (rxParams.channelNum == 0x02) {
                    // 通道6-10设置（组控制）
                    if (rxParams.bandwidthValid) {
                        channelGroupParams[1].bandwidth = rxParams.bandwidth;
                    }
                    channelGroupParams[1].workMode = rxParams.workMode;
                    channelGroupParams[1].frequency = rxParams.frequency;
                    channelGroupParams[1].rfAtten = rxParams.rfAtten;
                    channelGroupParams[1].ifAtten = rxParams.ifAtten;
                    
                    // 更新FPGA参数，直接使用rxParams.channelNum
                    FPGA_UpdateChannelParams(rxParams.channelNum);
                }
                
                break;
                
            case 0x0E:  // 调试用-产品信息设置 (自定义)
                // 数据格式：
                // BYTE2: 厂家代号
                // BYTE3-4: 出厂年份 (BCD)
                // BYTE5: 出厂月份 (BCD)
                // BYTE6: 出厂日期 (BCD)
                // BYTE7-8: 序列号年份 (BCD)
                // BYTE9: 序列号批次 (BCD)
                // BYTE10-11: 序列号序号 (BCD)
                if (CanDataLength >= 12)
                {
                    manufacturerCode = CanRxData[2];
                    productYear = (CanRxData[3] << 8) | CanRxData[4];
                    productMonth = CanRxData[5];
                    productDay = CanRxData[6];
                    serialYear = (CanRxData[7] << 8) | CanRxData[8];
                    serialBatch = CanRxData[9];
                    serialNum = (CanRxData[10] << 8) | CanRxData[11];
                    
                    // 保存到Flash
                    uint32_t productInfo[3];
                    productInfo[0] = (productYear << 16) | (productMonth << 8) | productDay;
                    productInfo[1] = (serialYear << 16) | (serialBatch << 8) | ((serialNum >> 8) & 0xFF);
                    productInfo[2] = (serialNum & 0xFF) << 8;
                    SetProductInfo(productInfo);
                    
                    // 更新已加载标志，确保下次读取使用新值
                    productInfoLoaded = 0;
                    
                    // 响应确认
                    CanSendData[0] = 0xF0;
                    CanSendData[1] = 0x0E;
                    CanSendData[2] = 0x00;  // 成功
                    CANxSend(CanSendData, 3, 0x04, MarkAddr);
                }
                break;
        }
    }
    else if (rxParams.msgCategory == 0xAA && rxParams.msgId == 0x55) {
        // 模块初始化 (0xAA55)
        // 初始化两组通道参数为默认值
        for (i = 0; i < 2; i++) {
            channelGroupParams[i].frequency = 100000;  // 默认100MHz
            channelGroupParams[i].rfAtten = 0;
            channelGroupParams[i].ifAtten = 0;
            channelGroupParams[i].bandwidth = 0;
            channelGroupParams[i].workMode = 0;
            channelGroupParams[i].powerState = 1;
        }
        
        // 这里可以添加硬件初始化代码
    }
}

/**
  * @brief  设置产品信息
  * @param  info -- 产品信息数组
  * @retval 无
  */
void SetProductInfo(uint32_t info[])
{
    STMFLASH_Write(STORE_ADDR, info, 3);
}
/*********************************************END OF FILE**********************/
