/**
  ******************************************************************************
  * @file       : can.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : CAN总线驱动实现
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */
  
/* 头文件包含 ----------------------------------------------------------------*/
#include "spi.h"
#include "sys.h"
#include "delay.h"

/* 变量定义 -------------------------------------------------------------------*/
SPI_HandleTypeDef hspi1;

/**
  * @brief  SPI1初始化
  * @param  无
  * @retval 无
  */
void SPI1_Init(void)
{
	hspi1.Instance = SPI1;
	hspi1.Init.Mode = SPI_MODE_MASTER;	// SPI工作模式：主模式			
	hspi1.Init.Direction = SPI_DIRECTION_2LINES;	// SPI数据模式：双线模式
	hspi1.Init.DataSize = SPI_DATASIZE_8BIT;	// SPI数据大小：16位
	hspi1.Init.CLKPolarity = SPI_POLARITY_LOW;	// 时钟空闲极性：低
	hspi1.Init.CLKPhase = SPI_PHASE_1EDGE;	// 数据采样沿：第一个时钟沿
	hspi1.Init.NSS = SPI_NSS_SOFT;	// NSS控制：软件管理
	hspi1.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_16;	// 时钟分频，18MHz
	hspi1.Init.FirstBit = SPI_FIRSTBIT_MSB;	// 数据传输高位先传
	hspi1.Init.TIMode = SPI_TIMODE_DISABLE;	// 
	hspi1.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;	// CRC关闭
	hspi1.Init.CRCPolynomial = 10;	// CRC计算的多项式
	if (HAL_SPI_Init(&hspi1) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
	
	// 在软件NSS模式下，必须设置SSI位为1，确保SPI工作在主模式
	hspi1.Instance->CR1 |= SPI_CR1_SSI;
	
	// 确保I2S模式被禁用
	hspi1.Instance->I2SCFGR &= ~SPI_I2SCFGR_I2SMOD;
	
	// 使能SPI1
	__HAL_SPI_ENABLE(&hspi1);
	
	// 添加延时确保SPI2配置稳定
	delay_ms(1);
}

/**
  * @brief  SPI1数据发送
  * @param  TxData[] -- 待发送数据
  * @retval 无
  */
void SPI1_SendData(uint8_t TxData[])
{
	SPI1_NSS = 1;
	delay_us(1);
	if (HAL_SPI_Transmit(&hspi1, TxData, 6, 500) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
	delay_us(1);
	SPI1_NSS = 0;
}

/**
  * @brief  SPI底层初始化函数
  * @param  hspi : SPI句柄
  * @retval 此函数会被 HAL_SPI_Init() 调用
  */
void HAL_SPI_MspInit(SPI_HandleTypeDef* hspi)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	
	if (hspi->Instance == SPI1)
	{
		// SPI1 时钟配置
		__HAL_RCC_GPIOA_CLK_ENABLE();  // 先使能GPIO时钟
		__HAL_RCC_SPI1_CLK_ENABLE();  // SPI1使用默认引脚，不需要AFIO
		
		/**SPI1 GPIO 配置
		PA4     ------> SPI1_NSS
		PA5     ------> SPI1_SCK
		PA6     ------> SPI1_MISO
		PA7     ------> SPI1_MOSI
		*/		
		// 然后配置NSS为输出
		GPIO_InitStruct.Pin = GPIO_PIN_4;
		GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
		HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
		
		// PA5 (SCK) 配置为复用推挽输出 - 特殊处理
		// 先确保PA5为高电平输出，避免影响其他外设
		GPIO_InitStruct.Pin = GPIO_PIN_5;
		GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
		HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
		HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5, GPIO_PIN_SET);  // 设置为高电平
		
		// 延时确保稳定
		delay_us(10);
		
		// 然后配置为SPI功能
		GPIO_InitStruct.Pin = GPIO_PIN_5;
		GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
		HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
		
		// PA7 (MOSI) 配置为复用推挽输出
		GPIO_InitStruct.Pin = GPIO_PIN_7;
		GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
		HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
		
		// PA6 (MISO) 配置为上拉输入
		GPIO_InitStruct.Pin = GPIO_PIN_6;
		GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
		GPIO_InitStruct.Pull = GPIO_PULLUP;
		HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
		
		SPI1_NSS = 1;  // 默认高电平，不选中
	}
}

/*********************************************END OF FILE**********************/

