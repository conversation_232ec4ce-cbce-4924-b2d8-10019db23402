#ifndef _FPGA_CTRL_H
#define _FPGA_CTRL_H

/* 头文件包含 ----------------------------------------------------------------*/
#include "stm32f1xx_hal.h"

/* FPGA通道控制定义 ----------------------------------------------------------*/
#define FPGA_CHANNEL_ALL		0x00	// 全通道
#define FPGA_CHANNEL_1TO5		0x01	// 通道1-5
#define FPGA_CHANNEL_6TO10		0x02	// 通道6-10

/* FPGA功能码定义 ------------------------------------------------------------*/
#define FPGA_FUNC_BANDWIDTH		0x01	// 带宽滤波器控制
#define FPGA_FUNC_WORKMODE		0x02	// 工作模式控制
#define FPGA_FUNC_RF_SWITCH		0x03	// 射频开关&滤波器控制
#define FPGA_FUNC_RF_ATTEN		0x04	// 射频衰减器控制
#define FPGA_FUNC_IF_ATTEN		0x05	// 中频衰减器控制
#define FPGA_FUNC_CAL_ATTEN		0x06	// 校正衰减器控制

/* 带宽定义 ------------------------------------------------------------------*/
#define BANDWIDTH_200KHZ		0x00	// 200kHz带宽
#define BANDWIDTH_5MHZ			0x01	// 5MHz带宽
#define BANDWIDTH_50MHZ			0x02	// 50MHz带宽

/* 工作模式定义 --------------------------------------------------------------*/
#define WORKMODE_LOWPOWER		0x00	// 低功耗模式
#define WORKMODE_NORMAL			0x01	// 正常模式

/* 函数声明 ------------------------------------------------------------------*/
void FPGA_SendCommand(uint8_t channel, uint8_t function, uint8_t data);
uint8_t FPGA_WaitResponse(uint8_t channel, uint8_t function);
void FPGA_SetBandwidth(uint8_t channel, uint8_t bandwidth);
void FPGA_SetWorkMode(uint8_t channel, uint8_t mode);
void FPGA_SetRfAtten(uint8_t channel, uint8_t atten);
void FPGA_SetIfAtten(uint8_t channel, uint8_t atten);
void FPGA_SetCalAtten(uint8_t channel, uint8_t atten);
void FPGA_SetFrequencyBand(uint8_t channel, uint32_t frequency);
void FPGA_UpdateChannelParams(uint8_t channel);
void FPGA_SendBatchCommands(uint8_t commands[][3], uint8_t count);

#endif

/*********************************************END OF FILE**********************/
