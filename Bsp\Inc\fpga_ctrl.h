#ifndef _FPGA_CTRL_H
#define _FPGA_CTRL_H

/* 头文件包含 ----------------------------------------------------------------*/
#include "stm32f1xx_hal.h"

/* 频综模块信道编号定义 ------------------------------------------------------*/
#define FPGA_CHANNEL_ALL        0x00    // 通道同时设置
#define FPGA_CHANNEL_LOW1       0x01    // 低段一本振1通道
#define FPGA_CHANNEL_LOW2       0x02    // 低段一本振2通道
#define FPGA_CHANNEL_LOW_SWITCH 0x03    // 低段一本振开关/功分切换
#define FPGA_CHANNEL_HIGH1_1    0x04    // 高段一本振1通道
#define FPGA_CHANNEL_HIGH2_1    0x05    // 高段二本振1通道
#define FPGA_CHANNEL_HIGH1_2    0x06    // 高段一本振2通道
#define FPGA_CHANNEL_HIGH2_2    0x07    // 高段二本振2通道
#define FPGA_CHANNEL_HIGH1_3    0x08    // 高段一本振3通道
#define FPGA_CHANNEL_HIGH2_3    0x09    // 高段二本振3通道

/* 工作模式定义 --------------------------------------------------------------*/
#define FPGA_MODE_SWITCH        0x00    // 低段一本振开关工作模式
#define FPGA_MODE_POWER_DIV     0x01    // 低段一本振功分工作模式
#define FPGA_MODE_LOW_POWER     0x02    // 低功耗工作模式

/* 频率范围定义 --------------------------------------------------------------*/
#define LOW_FREQ_MIN            30000       // 低段一本振最小频率 (kHz)
#define LOW_FREQ_MAX            3000000     // 低段一本振最大频率 (kHz)
#define HIGH_FREQ_MIN           2000000     // 高段本振最小频率 (kHz)
#define HIGH_FREQ_MAX           18000000    // 高段本振最大频率 (kHz)

/* FPGA频率范围定义 ----------------------------------------------------------*/
#define FPGA_LOW_FREQ_MIN       4000000     // FPGA低段一本振最小频率 (kHz)
#define FPGA_LOW_FREQ_MAX       6970000     // FPGA低段一本振最大频率 (kHz)
#define FPGA_HIGH1_FREQ_MIN     10200000    // FPGA高段一本振最小频率 (kHz)
#define FPGA_HIGH1_FREQ_MAX     20000000    // FPGA高段一本振最大频率 (kHz)
#define FPGA_HIGH2_FREQ_MIN     4970000     // FPGA高段二本振最小频率 (kHz)
#define FPGA_HIGH2_FREQ_MAX     7480000     // FPGA高段二本振最大频率 (kHz)

/* 函数声明 ------------------------------------------------------------------*/
void FPGA_SetChannelParams(uint8_t channelNum, uint8_t workMode, uint32_t lowFreq, uint32_t high1Freq, uint32_t high2Freq);
void FPGA_ReadParams(void);
int8_t FPGA_GetTemperature(void);
uint16_t FPGA_GetLockStatus(void);
void FPGA_ProcessChannelSet(uint8_t channelNum, uint8_t workMode, uint32_t lowFreq, uint32_t high1Freq, uint32_t high2Freq);

#endif

/*********************************************END OF FILE**********************/
