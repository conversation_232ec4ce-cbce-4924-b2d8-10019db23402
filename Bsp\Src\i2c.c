#include "i2c.h"

// SDA输出模式
static void SDA_OUT(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = I2C_SDA_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(I2C_SDA_PORT, &GPIO_InitStruct);
}

// SDA输入模式
static void SDA_IN(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = I2C_SDA_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;  // 不使用内部上拉，假设外部有上拉电阻
    HAL_GPIO_Init(I2C_SDA_PORT, &GPIO_InitStruct);
}

// 读取SDA电平
static uint8_t READ_SDA(void)
{
    return HAL_GPIO_ReadPin(I2C_SDA_PORT, I2C_SDA_PIN);
}

// 设置SCL电平
static void I2C_SCL(uint8_t level)
{
    HAL_GPIO_WritePin(I2C_SCL_PORT, I2C_SCL_PIN, level ? GPIO_PIN_SET : GPIO_PIN_RESET);
}

// 设置SDA电平
static void I2C_SDA(uint8_t level)
{
    HAL_GPIO_WritePin(I2C_SDA_PORT, I2C_SDA_PIN, level ? GPIO_PIN_SET : GPIO_PIN_RESET);
}

/**
 * @brief 软件I2C初始化
 * @param None
 * @retval None
 */
void Soft_I2C_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIO时钟
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();
    
    // 配置SCL引脚 (PB6)
    GPIO_InitStruct.Pin = I2C_SCL_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(I2C_SCL_PORT, &GPIO_InitStruct);
    
    // 配置SDA引脚 (PC12)
    GPIO_InitStruct.Pin = I2C_SDA_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(I2C_SDA_PORT, &GPIO_InitStruct);
    
    // 初始化为高电平
    I2C_SCL(1);
    I2C_SDA(1);
}

/**
 * @brief 产生I2C起始信号
 * @param None
 * @retval None
 */
void Soft_I2C_Start(void)
{
    SDA_OUT();
    I2C_SDA(1);
    I2C_SCL(1);
    I2C_DELAY();
    I2C_SDA(0); // START: when CLK is high, DATA change from high to low
    I2C_DELAY();
    I2C_SCL(0); // 钳住I2C总线，准备发送或接收数据
}

/**
 * @brief 产生I2C停止信号
 * @param None
 * @retval None
 */
void Soft_I2C_Stop(void)
{
    SDA_OUT();
    I2C_SCL(0);
    I2C_SDA(0); // STOP: when CLK is high, DATA change from low to high
    I2C_DELAY();
    I2C_SCL(1);
    I2C_DELAY();
    I2C_SDA(1); // 发送I2C总线结束信号
    I2C_DELAY();
}

/**
 * @brief 等待应答信号到来
 * @param None
 * @retval 0: 接收应答成功; 1: 接收应答失败
 */
uint8_t Soft_I2C_WaitAck(void)
{
    uint8_t ucErrTime = 0;
    SDA_IN();
    I2C_SDA(1);
    I2C_DELAY();
    I2C_SCL(1);
    I2C_DELAY();
    
    while(READ_SDA())
    {
        ucErrTime++;
        if(ucErrTime > 250)
        {
            Soft_I2C_Stop();
            return 1;
        }
    }
    I2C_SCL(0);
    return 0;
}

/**
 * @brief 产生ACK应答
 * @param None
 * @retval None
 */
void Soft_I2C_Ack(void)
{
    I2C_SCL(0);
    SDA_OUT();
    I2C_SDA(0);
    I2C_DELAY();
    I2C_SCL(1);
    I2C_DELAY();
    I2C_SCL(0);
}

/**
 * @brief 不产生ACK应答
 * @param None
 * @retval None
 */
void Soft_I2C_NAck(void)
{
    I2C_SCL(0);
    SDA_OUT();
    I2C_SDA(1);
    I2C_DELAY();
    I2C_SCL(1);
    I2C_DELAY();
    I2C_SCL(0);
}

/**
 * @brief I2C发送一个字节
 * @param byte: 要发送的字节
 * @retval None
 */
void Soft_I2C_SendByte(uint8_t byte)
{
    uint8_t t;
    SDA_OUT();
    I2C_SCL(0);
    
    for(t = 0; t < 8; t++)
    {
        I2C_SDA((byte & 0x80) >> 7);
        byte <<= 1;
        I2C_DELAY();
        I2C_SCL(1);
        I2C_DELAY();
        I2C_SCL(0);
        I2C_DELAY();
    }
}

/**
 * @brief 读一个字节
 * @param ack: ack=1时，发送ACK; ack=0，发送nACK
 * @retval 读取到的字节
 */
uint8_t Soft_I2C_ReadByte(uint8_t ack)
{
    uint8_t i, receive = 0;
    SDA_IN();
    
    for(i = 0; i < 8; i++)
    {
        I2C_SCL(0);
        I2C_DELAY();
        I2C_SCL(1);
        receive <<= 1;
        if(READ_SDA()) receive++;
        I2C_DELAY();
    }
    
    if(!ack)
        Soft_I2C_NAck();
    else
        Soft_I2C_Ack();
        
    return receive;
}

/**
 * @brief 软件I2C主机发送数据
 * @param DevAddress: 设备地址（7位地址）
 * @param pData: 数据指针
 * @param Size: 数据大小
 * @retval HAL状态
 */
HAL_StatusTypeDef Soft_I2C_Master_Transmit(uint16_t DevAddress, uint8_t *pData, uint16_t Size)
{
    uint16_t i;
    
    Soft_I2C_Start();
    Soft_I2C_SendByte((DevAddress << 1) | 0); // 发送设备地址+写命令
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    for(i = 0; i < Size; i++)
    {
        Soft_I2C_SendByte(pData[i]);
        if(Soft_I2C_WaitAck())
        {
            Soft_I2C_Stop();
            return HAL_ERROR;
        }
    }
    
    Soft_I2C_Stop();
    return HAL_OK;
}

/**
 * @brief 软件I2C主机接收数据
 * @param DevAddress: 设备地址（7位地址）
 * @param pData: 数据指针
 * @param Size: 数据大小
 * @retval HAL状态
 */
HAL_StatusTypeDef Soft_I2C_Master_Receive(uint16_t DevAddress, uint8_t *pData, uint16_t Size)
{
    uint16_t i;
    
    Soft_I2C_Start();
    Soft_I2C_SendByte((DevAddress << 1) | 1); // 发送设备地址+读命令
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    for(i = 0; i < Size; i++)
    {
        if(i < Size - 1)
            pData[i] = Soft_I2C_ReadByte(1); // 读数据，发送ACK
        else
            pData[i] = Soft_I2C_ReadByte(0); // 最后一个字节，发送NACK
    }
    
    Soft_I2C_Stop();
    return HAL_OK;
}

/**
 * @brief 软件I2C内存写入（适用于LTC2945寄存器写入）
 * @param DevAddress: 设备地址（7位地址）
 * @param MemAddress: 内存/寄存器地址
 * @param MemAddSize: 地址大小（I2C_MEMADD_SIZE_8BIT）
 * @param pData: 数据指针
 * @param Size: 数据大小
 * @retval HAL状态
 */
HAL_StatusTypeDef Soft_I2C_Mem_Write(uint16_t DevAddress, uint16_t MemAddress, uint16_t MemAddSize, uint8_t *pData, uint16_t Size)
{
    uint16_t i;
    
    Soft_I2C_Start();
    Soft_I2C_SendByte((DevAddress << 1) | 0); // 发送设备地址+写命令
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 发送寄存器地址
    Soft_I2C_SendByte(MemAddress & 0xFF);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 发送数据
    for(i = 0; i < Size; i++)
    {
        Soft_I2C_SendByte(pData[i]);
        if(Soft_I2C_WaitAck())
        {
            Soft_I2C_Stop();
            return HAL_ERROR;
        }
    }
    
    Soft_I2C_Stop();
    return HAL_OK;
}

/**
 * @brief 软件I2C内存读取（适用于LTC2945寄存器读取）
 * @param DevAddress: 设备地址（7位地址）
 * @param MemAddress: 内存/寄存器地址
 * @param MemAddSize: 地址大小（I2C_MEMADD_SIZE_8BIT）
 * @param pData: 数据指针
 * @param Size: 数据大小
 * @retval HAL状态
 */
HAL_StatusTypeDef Soft_I2C_Mem_Read(uint16_t DevAddress, uint16_t MemAddress, uint16_t MemAddSize, uint8_t *pData, uint16_t Size)
{
    uint16_t i;
    
    // 按照C51代码的时序：
    // 1. 发送START
    // 2. 发送设备地址+写命令(0xCC)
    // 3. 发送寄存器地址
    // 4. 发送重复START
    // 5. 发送设备地址+读命令(0xCF)
    // 6. 读取数据
    // 7. 发送STOP
    
    Soft_I2C_Start();
    // 注意：C51使用的是8位地址格式，我们需要转换
    Soft_I2C_SendByte((DevAddress << 1) | 0); // 发送设备地址+写命令
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    Soft_I2C_SendByte(MemAddress & 0xFF); // 发送寄存器地址
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 重复起始条件
    Soft_I2C_Start();
    Soft_I2C_SendByte((DevAddress << 1) | 1); // 发送设备地址+读命令
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 读取数据
    for(i = 0; i < Size; i++)
    {
        if(i < Size - 1)
            pData[i] = Soft_I2C_ReadByte(1); // 读数据，发送ACK
        else
            pData[i] = Soft_I2C_ReadByte(0); // 最后一个字节，发送NACK
    }
    
    Soft_I2C_Stop();
    return HAL_OK;
}
