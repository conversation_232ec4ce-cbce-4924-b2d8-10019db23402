## 3 CAN总线数据ICD
### 3.1 通侦频综模块接收的CAN总线数据场帧格式
##### 3.1.1 通侦频综模块接收的CAN总线数据场帧格式
优先级：参数设置及查询命令（0x02）。

表1 模块产品信息查询的数据场帧格式

|字节|数据位|信号描述|
|---|---|---|
|BYTE0|BIT7-BIT0|消息类别：0xF0|
|BYTE1|BIT7-BIT0|消息ID：0x00 查询本模块的产品信息|

##### 3.1.2 模块BIT查询
优先级：参数设置及查询命令（0x02）。

表2 模块BIT查询的数据场帧格式

|字节|数据位|信号描述|
|---|---|---|
|BYTE0|BIT7-BIT0|消息类别：0xF0|
|BYTE1|BIT7-BIT0|消息ID：0x01 查询本模块BIT|

##### 3.1.3 通道参数查询
优先级：参数设置及查询命令（0x02）。

表3 模块通道参数查询的数据场帧格式

| 字节    | 数据位       | 信号描述                                                                                                                                                                                                                                                                     |
| ----- | --------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| BYTE0 | BIT7‑BIT0 | 消息类别：`0xF0`                                                                                                                                                                                                                                                              |
| BYTE1 | BIT7‑BIT0 | 消息 ID：`0x02` —— 查询模块通道参数                                                                                                                                                                                                                                                 |
| BYTE2 | BIT7‑BIT0 | **信道编号**：<br>`0x00` 通道同时查询；<br>`0x01` 低段一 本振 1通道参数查询；<br>`0x02` 低段一 本振 2通道参数查询；<br>`0x03` 低段一 本振开关/功分切换参数设置；<br>`0x04` 高段一 本振 1通道参数查询；<br>`0x05` 高段二 本振 1通道参数查询；<br>`0x06` 高段一 本振 2通道参数查询；<br>`0x07` 高段二 本振 2通道参数查询；<br>`0x08` 高段一 本振 3通道参数查询；<br>`0x09` 高段二 本振 3通道参数查询； |

##### 3.1.4 通道参数设置

优先级：参数设置及查询命令（`0x02`）。

表 4 模块通道参数设置的数据场帧格式

| 字节              | 数据位        | 信号描述                                                                                                                                                                                                                                                                     |
| --------------- | ---------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| BYTE0           | BIT7‑BIT0  | 消息类别：`0xF0`                                                                                                                                                                                                                                                              |
| BYTE1           | BIT7‑BIT0  | 消息 ID：`0x04`                                                                                                                                                                                                                                                             |
| BYTE2           | BIT7‑BIT0  | **信道编号**：<br>`0x00` 通道同时查询；<br>`0x01` 低段一 本振 1通道参数设置；<br>`0x02` 低段一 本振 2通道参数设置；<br>`0x03` 低段一 本振开关/功分切换参数设置；<br>`0x04` 高段一 本振 1通道参数设置；<br>`0x05` 高段二 本振 1通道参数设置；<br>`0x06` 高段一 本振 2通道参数设置；<br>`0x07` 高段二 本振 2通道参数设置；<br>`0x08` 高段一 本振 3通道参数设置；<br>`0x09` 高段二 本振 3通道参数设置； |
| BYTE3           | BIT7‑BIT2  | **保留**                                                                                                                                                                                                                                                                   |
| BYTE3           | BIT1‑BIT0  | 工作模式<br>`00` 低段一本振开关工作模式；<br>`01` 低段一本振功分工作模式；<br>`02` 低功耗工作模式                                                                                                                                                                                                           |
| BYTE4 ~ BYTE7   | BIT31‑BIT0 | 低段一本振**中心频率** (kHz)，范围 `[30000 ~ 3000000]`<br>单位：kHz；<br>精度：1kHz；<br>对应值域：`[30000 ~ 3000000]`<br>数据类型：无符号整型。                                                                                                                                                             |
| BYTE8 ~ BYTE11  | BIT31‑BIT0 | 高段一本振**中心频率** (kHz)，范围 `[2000000 ~ 18000000]`<br>单位：kHz；<br>精度：1kHz；<br>对应值域：`[2000000 ~ 18000000]`<br>数据类型：无符号整型。                                                                                                                                                       |
| BYTE12 ~ BYTE15 | BIT31‑BIT0 | 高段二本振**中心频率** (kHz)，范围 `[2000000 ~ 18000000]`<br>单位：kHz；<br>精度：1kHz；<br>对应值域：`[2000000 ~ 18000000]`<br>数据类型：无符号整型。                                                                                                                                                       |

### 3.2 通侦频综模块**发送**的CAN总线数据场帧格式

##### 3.2.1 模块产品信息查询响应

优先级：遥测/状态查询应答数据（`0x04`）。

表 5 模块产品信息查询响应的数据场帧格式

| 字节              | 数据位        | 信号描述                                                                                                   |
| --------------- | ---------- | ------------------------------------------------------------------------------------------------------ |
| BYTE0           | BIT7‑BIT0  | 消息类别：`0xF0`                                                                                            |
| BYTE1           | BIT7‑BIT0  | 消息 ID：`0x00` —— 产品信息响应                                                                                 |
| BYTE2           | BIT7‑BIT0  | **厂商代号**：<br>0x00：十所<br>0x01：振芯科技<br>0x02：兴仁科技<br>0x03：美数<br>0x04：中电52所<br>0x05：联邦微波                   |
| BYTE3           | BIT7-BIT4  | 出厂时间-年 1000_BCD(4bit)<br>描述：<br>该域BCD码表示                                                               |
| BYTE3           | BIT3-BIT0  | 出厂时间-年 100_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                |
| BYTE4           | BIT7-BIT4  | 出厂时间-年 10_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                 |
| BYTE4           | BIT3-BIT0  | 出厂时间-年_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                    |
| BYTE5           | BIT7-BIT4  | 出厂时间-月10_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                  |
| BYTE5           | BIT3-BIT0  | 出厂时间-月_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                    |
| BYTE6           | BIT7-BIT4  | 出厂时间-日 10_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                 |
| BYTE6           | BIT3-BIT0  | 出厂时间-日_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                    |
| BYTE7           | BIT7-BIT4  | 生产序列号-年 1000_BCD(4bit)<br>描述：<br>该域BCD码表示                                                              |
| BYTE7           | BIT3-BIT0  | 生产序列号-年 100_BCD(4bit)<br>描述：<br>该域BCD码表示                                                               |
| BYTE8           | BIT7-BIT4  | 生产序列号-年 10_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                |
| BYTE8           | BIT3-BIT0  | 生产序列号-年_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                   |
| BYTE9           | BIT7-BIT4  | 生产序列号-批次 10_BCD(4bit)<br>描述：<br>该域BCD码表示                                                               |
| BYTE9           | BIT3-BIT0  | 生产序列号-批次_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                  |
| BYTE10          | BIT7-BIT4  | 生产序列号-保留 8-4位(4bit)<br>描述:<br>表示空闲4位                                                                   |
| BYTE10          | BIT3-BIT0  | 生产序列号-序号 100_BCD(4bit)<br>描述：<br>该域BCD码表示                                                              |
| BYTE11          | BIT7-BIT4  | 生产序列号-序号 10_BCD(4bit)<br>描述：<br>该域BCD码表示                                                               |
| BYTE11          | BIT3-BIT0  | 生产序列号-序号_BCD(4bit)<br>描述：<br>该域BCD码表示                                                                  |
| BYTE12          | BIT7-BIT0  | MARK地址：根据背板槽位地址读取低7bit，第8bit补0                                                                         |
| BYTE13 ～ BYTE16 | BIT31-BIT0 | 低段一本振工作频率L：表示工作频段下限值；  <br>取值：30000；  <br>单位：kHz；  <br>精度1kHz；  <br>对应值域30000kHz；  <br>数据类型：无符号整型;     |
| BYTE17 ～ BYTE20 | BIT31-BIT0 | 低段一本振工作频率H：表示工作频段上限值；<br>取值：3000000；<br>单位：kHz；<br>精度1kHz；<br>对应值域3000000kHz；<br>数据类型：无符号整型;           |
| BYTE21 ～ BYTE24 | BIT31-BIT0 | 高段一本振工作频率L：表示工作频段下限值；  <br>取值：2000000；  <br>单位：kHz；  <br>精度1kHz；  <br>对应值域2000000kHz；  <br>数据类型：无符号整型; |
| BYTE25 ～ BYTE28 | BIT31-BIT0 | 高段一本振工作频率H：表示工作频段上限值；<br>取值：18000000；<br>单位：kHz；<br>精度1kHz；<br>对应值域18000000kHz；<br>数据类型：无符号整型;         |
| BYTE29 ～ BYTE32 | BIT31-BIT0 | 高段二本振工作频率L：表示工作频段下限值；  <br>取值：2000000；  <br>单位：kHz；  <br>精度1kHz；  <br>对应值域2000000kHz；  <br>数据类型：无符号整型; |
| BYTE33 ～ BYTE36 | BIT31-BIT0 | 高段二本振工作频率H：表示工作频段上限值；<br>取值：18000000；<br>单位：kHz；<br>精度1kHz；<br>对应值域18000000kHz；<br>数据类型：无符号整型;         |

##### 3.2.2 模块BIT查询响应
优先级：遥测/状态查询应答数据（`0x04`）。

表6 模块BIT查询响应的数据场帧格式

| 字节                    | 数据位                 | 信号描述                                                                                            |
| --------------------- | ------------------- | ----------------------------------------------------------------------------------------------- |
| BYTE0                 | BIT7-BIT0           | 消息类别：0xF0                                                                                       |
| BYTE1                 | BIT7-BIT0           | 消息ID：0x01                                                                                       |
| BYTE2                 | BIT7-BIT0           | 当前温度检测值，取值范围[-55-75]；<br>单位：℃；<br>精度：1℃；<br>对应值域：[-55-75]℃；<br>数据类型：有符号整型。                      |
| BYTE3<br>～<br>BYTE10  | BIT7-BIT0           | 分别对应通道1~8 +5.5V检测电压，取值范围[0-65]；<br>单位：V；<br>精度：0.1V；<br>对应值域：[0-6.5]V；<br>数据类型：无符号整型。<br>无效0xFF |
| BYTE11                | BIT7-BIT0           | -5V检测电压，取值范围[-75-0]；<br>单位：V；<br>精度：0.1V；<br>对应值域：[-7.5-0]V；<br>数据类型：无符号整型。<br>无效0xFF           |
| BYTE12<br>～<br>BYTE13 | BIT10<br>～<br>BIT15 | 保留                                                                                              |
| BYTE12<br>～<br>BYTE13 | BIT9                | 高段二本振3通道锁定指示：<br>0—未锁定；<br>1—已锁定；                                                               |
| BYTE12<br>～<br>BYTE13 | BIT8                | 高段一本振3通道锁定指示：<br>0—未锁定；<br>1—已锁定；                                                               |
| BYTE12<br>～<br>BYTE13 | BIT7                | 高段二本振2通道锁定指示：<br>0—未锁定；<br>1—已锁定；                                                               |
| BYTE12<br>～<br>BYTE13 | BIT6                | 高段一本振2通道锁定指示：<br>0—未锁定；<br>1—已锁定；                                                               |
| BYTE12<br>～<br>BYTE13 | BIT5                | 高段二本振1通道锁定指示：<br>0—未锁定；<br>1—已锁定；                                                               |
| BYTE12<br>～<br>BYTE13 | BIT4                | 高段一本振1通道锁定指示：<br>0—未锁定；<br>1—已锁定；                                                               |
| BYTE12<br>～<br>BYTE13 | BIT3                | 低段二本振本振参考输入信号状态检测结果<br>0—异常；<br>1—正常；                                                           |
| BYTE12<br>～<br>BYTE13 | BIT2                | 低段二本振通道锁定指示：<br>0—未锁定；<br>1—已锁定；                                                                |
| BYTE12<br>～<br>BYTE13 | BIT1                | 低段一本振2通道锁定指示：<br>0—未锁定；<br>1—已锁定；                                                               |
| BYTE12<br>～<br>BYTE13 | BIT0                | 低段一本振1通道锁定指示：<br>0—未锁定；<br>1—已锁定；                                                               |

##### 3.2.3 模块通道参数查询响应
优先级：遥测/状态查询应答数据（`0x04`）。

表7 模块通道参数查询响应的数据场帧格式

| 字节                    | 数据位        | 信号描述                                                                                                                                                                                                                            |
| --------------------- | ---------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| BYTE0                 | BIT7-BIT0  | 消息类别：0xF0                                                                                                                                                                                                                       |
| BYTE1                 | BIT7-BIT0  | 消息ID：0x02                                                                                                                                                                                                                       |
| BYTE2                 | BIT7-BIT0  | 信道编号：<br>0x00：通道同时查询；<br>0x01：低段一本振1通道参数查询；<br>0x02：低段一本振2通道参数查询；<br>0x03：低段一本振开关/功分切换参数查询；<br>0x04：高段一本振1通道参数查询；<br>0x05：高段二本振1通道参数查询；<br>0x06：高段一本振2通道参数查询；<br>0x07：高段二本振2通道参数查询；<br>0x08：高段一本振3通道参数查询；<br>0x09：高段二本振3通道参数查询； |
| BYTE3                 | BIT7-BIT0  | 低段二本振参考输入信号状态信息回传：<br>0：异常；<br>1：正常；<br>                                                                                                                                                                                        |
| BYTE4<br>～<br>BYTE7   | BIT31-BIT0 | 低段一本振N通道频率参数；取值范围[30000-3000000]；<br>单位：kHz；<br>精度：1kHz；<br>对应值域：[30000-3000000]kHz；<br>当BYTE2为0或1时有效，N=1；<br>当BYTE2为2时有效，N=2；<br>当BYTE2为其它时，该部分无效，值为0xFFFFFFFF；                                                                |
| BYTE8<br>～<br>BYTE11  | BIT31-BIT0 | 高段一本振N通道频率参数；取值范围[2000000-18000000]；<br>单位：kHz；<br>精度：1kHz；<br>对应值域：[2000000-18000000]kHz；<br>当BYTE2为0、4或5时有效，N=1；<br>当BYTE2为6或7时有效，N=2；<br>当BYTE2为8或9时有效，N=3；<br>当BYTE2为其它时，该部分无效，值为0xFFFFFFFF；                                |
| BYTE12<br>～<br>BYTE15 | BIT31-BIT0 | 高段二本振N通道频率参数；取值范围[2000000-18000000]；<br>单位：kHz；<br>精度：1kHz；<br>对应值域：[2000000-18000000]kHz；<br>当BYTE2为0、4或5时有效，N=1；<br>当BYTE2为6或7时有效，N=2；<br>当BYTE2为8或9时有效，N=3；<br>当BYTE2为其它时，该部分无效，值为0xFFFFFFFF；                                |
| BYTE16                | BIT7-BIT2  | 保留                                                                                                                                                                                                                              |
| BYTE16                | BIT1-BIT0  | 工作模式：<br>00—低段一本振功分工作模式；<br>01—低段一本振开关工作模式；<br>02—低功耗工作模式；                                                                                                                                                                      |
| BYTE17<br>～<br>BYTE20 | BIT31-BIT0 | 低段一本振2通道频率参数；取值范围[30000-3000000]；<br>单位：kHz；<br>精度：1kHz；<br>对应值域：[30000-3000000]kHz；<br>仅BYTE2为0时有效<br>无效时该条响应长度仅为BYTE0~BYTE16                                                                                                  |
| BYTE21<br>～<br>BYTE24 | BIT31-BIT0 | 高段一本振2通道频率参数；取值范围[2000000-18000000]；<br>单位：kHz；<br>精度：1kHz；<br>对应值域：[2000000-18000000]kHz；<br>仅BYTE2为0时有效<br>无效时该条响应长度仅为BYTE0~BYTE16                                                                                            |
| BYTE25<br>～<br>BYTE28 | BIT31-BIT0 | 高段二本振2通道频率参数；取值范围[2000000-18000000]；<br>单位：kHz；<br>精度：1kHz；<br>对应值域：[2000000-18000000]kHz；<br>仅BYTE2为0时有效<br>无效时该条响应长度仅为BYTE0~BYTE16                                                                                            |
| BYTE29<br>～<br>BYTE32 | BIT31-BIT0 | 高段一本振3通道频率参数；取值范围[2000000-18000000]；<br>单位：kHz；<br>精度：1kHz；<br>对应值域：[2000000-18000000]kHz；<br>仅BYTE2为0时有效<br>无效时该条响应长度仅为BYTE0~BYTE16                                                                                            |
| BYTE33<br>～<br>BYTE36 | BIT31-BIT0 | 高段二本振3通道频率参数；取值范围[2000000-18000000]；<br>单位：kHz；<br>精度：1kHz；<br>对应值域：[2000000-18000000]kHz；<br>仅BYTE2为0时有效<br>无效时该条响应长度仅为BYTE0~BYTE16                                                                                            |
