# CLO_CAN软件需求 - 通信协议

## 1. 工作平台

- **MCU**: GD32F107RCT6

## 2. 功能说明

### 功能框图

- MCU与外部CAN通信
- MCU与内部SPI通信
- 处理电流电压检测和温度回传等功能

## 3. 通信协议（内部SPI）

### 3.1 大端模式

#### 参数设置（写操作）

| 字节            | 位          | 描述                                                                                                                                                                                                                                        |
| ------------- | ---------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **BYTE0**     | BIT7-BIT0  | 帧头 0xF8                                                                                                                                                                                                                                   |
| **BYTE1**     | BIT7-BIT0  | 读写指令：0-写，1-读                                                                                                                                                                                                                              |
| **BYTE2~3**   | BIT15-BIT0 | 开关控制（1：开电，0：关电）<br>- Bit0：LLO1_1<br>- Bit1：LLO1_2<br>- Bit2：LLO2_1<br>- Bit3：LLO2_2<br>- Bit4：HLO1_1<br>- Bit5：HLO1_2<br>- Bit6：HLO1_3<br>- Bit7：HLO2_1<br>- Bit8：HLO2_2<br>- Bit9：HLO2_3<br>注：上电默认为高；LLO2_1与LLO2_2都为0时，L2LO_C=0          |
| **BYTE4**     | BIT7-BIT0  | 信道编号：<br>- 0x00：通道同时设置<br>- 0x01：低段一本振1通道参数设置<br>- 0x02：低段一本振2通道参数设置<br>- 0x03：低段一本振开关/功分切换参数设置<br>- 0x04：高段一本振1通道参数设置<br>- 0x05：高段二本振1通道参数设置<br>- 0x06：高段一本振2通道参数设置<br>- 0x07：高段二本振2通道参数设置<br>- 0x08：高段一本振3通道参数设置<br>- 0x09：高段二本振3通道参数设置 |
| **BYTE5**     | BIT7-BIT0  | Bit0:0:LLO2_1关 1：LLO2_1开<br>Bit1:0:LLO2_2关 1：LLO2_2开                                                                                                                                                                                      |
| **BYTE6**     | BIT7-BIT0  | 工作模式：<br>- 00：低段一本振开关工作模式<br>- 01：低段一本振功分工作模式<br>- 02：低功耗工作模式                                                                                                                                                                             |
| **BYTE7~10**  | BIT31-BIT0 | 低段一本振中心频率控制：<br>- 取值范围：[4000000-6970000]<br>- 单位：kHz<br>- 精度：1kHz<br>- 对应值域：[4000000-6970000]kHz<br>- 数据类型：无符号整型                                                                                                                          |
| **BYTE11~14** | BIT31-BIT0 | 高段一本振中心频率控制：<br>- 取值范围：[10200000-20000000]<br>- 单位：kHz<br>- 精度：1kHz<br>- 对应值域：[10200000-20000000]kHz<br>- 数据类型：无符号整型                                                                                                                      |
| **BYTE15~18** | BIT31-BIT0 | 高段二本振中心频率控制：<br>- 取值范围：[4970000-7480000]<br>- 单位：kHz<br>- 精度：1kHz<br>- 对应值域：[4970000-7480000]kHz<br>- 数据类型：无符号整型                                                                                                                          |
| **BYTE19**    | BIT7-BIT0  | 0x00 衰减（内部校准用）                                                                                                                                                                                                                            |

#### 读取参数（读操作）

| 字节        | 位        | 描述                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| ----------- | --------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **BYTE0**   | BIT7-BIT0 | 帧头 0xF8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| **BYTE1**   | BIT7-BIT0 | 读写指令：0-写，1-读                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| **BYTE2**   | BIT7-BIT0 | 当前温度检测值：<br>- 取值范围：[-55~75]<br>- 单位：°C<br>- 精度：1°C<br>- 对应值域：[-55~75]°C<br>- 数据类型：有符号整型                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| **BYTE3~4** | -         | 锁定状态指示：<br>- BIT0：低端一本振1通道锁定指示（0-未锁定，1-已锁定）<br>- BIT1：低端一本振2通道锁定指示（0-未锁定，1-已锁定）<br>- BIT2：低端二本振通道锁定指示（0-未锁定，1-已锁定）<br>- BIT3：低端二本振参考输入信号状态检测查询（0-异常，1-正常）<br>- BIT4：高端一本振1通道锁定指示（0-未锁定，1-已锁定）<br>- BIT5：高端二本振1通道锁定指示（0-未锁定，1-已锁定）<br>- BIT6：高端一本振2通道锁定指示（0-未锁定，1-已锁定）<br>- BIT7：高端二本振2通道锁定指示（0-未锁定，1-已锁定）<br>- BIT8：高端一本振3通道锁定指示（0-未锁定，1-已锁定）<br>- BIT9：高端二本振3通道锁定指示（0-未锁定，1-已锁定） |
| **BYTE4**   | -         | 保留                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

### 3.2 SPI通信时序

- 时钟信号（SCK）：CKPOL=0, CKPHA=0
- 数据传输：MSB优先
- 片选信号（NSS）：线方式

## 4. 控制管脚定义

| GD32F107RCT6管脚号 | GPIO | 管脚标识              | 功能描述          | I/O | 备注                 |
| --------------- | ---- | ----------------- | ------------- | --- | ------------------ |
| 14              | PA0  | LED               | 灯灯            | O   | 电源指示用              |
| 16              | PA2  | FPGA_ARM_RX       | 串口通信          | I   | 预留与内部模块串口通信        |
| 17              | PA3  | FPGA_RAM_TX       | 串口通信          | O   | 预留与内部模块串口通信        |
| 20              | PA4  | FPGA_ARM_SPI_NSS  | SPI通信         | O   | 与内部模块SPI通信         |
| 21              | PA5  | FPGA_ARM_SPI_CLK  | SPI通信         | O   | 与内部模块SPI通信         |
| 22              | PA6  | FPGA_ARM_SPI_MISO | SPI通信         | I   | 与内部模块SPI通信         |
| 23              | PA7  | FPGA_ARM_SPI_MOSI | SPI通信         | O   | 与内部模块SPI通信         |
| 24              | PC4  | N186_C            | NC7SZ18P6X控制位 | O   | NC7SZ18P6X         |
| 28              | PB2  | CS_SPI            | NC7SZ18P6X输入  | O   | NC7SZ18P6X         |
| 25              | PC5  | MARK0             | 模块编号          | I   | 单片机管脚配置上拉          |
| 26              | PB0  | MARK1             | 模块编号          | I   | 单片机管脚配置上拉          |
| 29              | PB10 | MARK2             | 模块编号          | I   | 单片机管脚配置上拉          |
| 38              | PC7  | MARK3             | 模块编号          | I   | 单片机管脚配置上拉          |
| 39              | PC8  | MARK4             | 模块编号          | I   | 单片机管脚配置上拉          |
| 40              | PC9  | MARK5             | 模块编号          | I   | 单片机管脚配置上拉          |
| 41              | PA8  | MARK6             | 模块编号          | I   | 单片机管脚配置上拉          |
| 30              | PB11 | SYS_RST_M         | 复位            | I   | 内部下拉               |
| 37              | PC6  | MCU1_IO_PC1       | 温度传感器通信位      | -   | GX18B20U           |
| 42              | PA9  | UART1_TX          | 串口            | O   | 预留串口               |
| 43              | PA10 | UART1_RX          | 串口            | I   | 预留串口               |
| 33              | PB12 | S230_RXD_1        | CAN通信         | O   | CAN1 SIT65HVD230   |
| 34              | PB13 | S230_TXD_1        | CAN通信         | I   | CAN1 SIT65HVD230   |
| 44              | PA11 | S230_RXD_0        | CAN通信         | O   | CAN0 SIT65HVD230   |
| 45              | PA12 | S230_TXD_0        | CAN通信         | I   | CAN0 SIT65HVD230   |
| 50              | PA15 | CS_SPI_FLASH      | 外挂FLASH通信位    | O   | 外挂FLASH EFM25F128A |
| 55              | PB3  | SCK_SPI           | 外挂FLASH通信位    | O   | 外挂FLASH EFM25F128A |
| 56              | PB4  | MISO_SPI          | 外挂FLASH通信位    | I   | 外挂FLASH EFM25F128A |
| 57              | PB5  | MOSI_SPI          | 外挂FLASH通信位    | O   | 外挂FLASH EFM25F128A |
| 52              | PC11 | SCL               | LTC2945IMS通信位 | -   | LTC2945IMS         |
| 53              | PC12 | SDA               | LTC2945IMS通信位 | -   | LTC2945IMS         |
| 54              | PD2  | ALERT             | LTC2945IMS通信位 | -   | LTC2945IMS         |
