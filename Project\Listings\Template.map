Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f107xc.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f107xc.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f107xc.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f107xc.o(RESET) refers to startup_stm32f107xc.o(STACK) for __initial_sp
    startup_stm32f107xc.o(RESET) refers to startup_stm32f107xc.o(.text) for Reset_Handler
    startup_stm32f107xc.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f107xc.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f107xc.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f107xc.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f107xc.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f107xc.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f107xc.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f107xc.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f107xc.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f107xc.o(RESET) refers to can.o(i.CAN1_RX0_IRQHandler) for CAN1_RX0_IRQHandler
    startup_stm32f107xc.o(RESET) refers to timer.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f107xc.o(RESET) refers to usart.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f107xc.o(RESET) refers to can.o(i.CAN2_RX0_IRQHandler) for CAN2_RX0_IRQHandler
    startup_stm32f107xc.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f107xc.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f107xc.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f107xc.o(.text) refers to startup_stm32f107xc.o(HEAP) for Heap_Mem
    startup_stm32f107xc.o(.text) refers to startup_stm32f107xc.o(STACK) for Stack_Mem
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f1xx_hal_adc.o(i.ADC_DMAError) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f1xx_hal_adc.o(i.ADC_Enable) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.ADC_Enable) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f1xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f1xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f1xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) for HAL_RCCEx_GetPeriphCLKFreq
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable) for ADC_ConversionStop_Disable
    stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_can.o(i.HAL_CAN_DeInit) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Stop) for HAL_CAN_Stop
    stm32f1xx_hal_can.o(i.HAL_CAN_DeInit) refers to stm32f1xx_hal_can.o(i.HAL_CAN_MspDeInit) for HAL_CAN_MspDeInit
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback) for HAL_CAN_TxMailbox0CompleteCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback) for HAL_CAN_TxMailbox0AbortCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback) for HAL_CAN_TxMailbox1CompleteCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback) for HAL_CAN_TxMailbox1AbortCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback) for HAL_CAN_TxMailbox2CompleteCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback) for HAL_CAN_TxMailbox2AbortCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback) for HAL_CAN_RxFifo0FullCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) for HAL_CAN_RxFifo0MsgPendingCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback) for HAL_CAN_RxFifo1FullCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback) for HAL_CAN_RxFifo1MsgPendingCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_SleepCallback) for HAL_CAN_SleepCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback) for HAL_CAN_WakeUpFromRxMsgCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ErrorCallback) for HAL_CAN_ErrorCallback
    stm32f1xx_hal_can.o(i.HAL_CAN_Init) refers to can.o(i.HAL_CAN_MspInit) for HAL_CAN_MspInit
    stm32f1xx_hal_can.o(i.HAL_CAN_Init) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_can.o(i.HAL_CAN_Start) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_can.o(i.HAL_CAN_Stop) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData) for FLASH_OB_ProgramData
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal_rcc.o(i.RCC_Delay) for RCC_Delay
    stm32f1xx_hal_rcc.o(i.RCC_Delay) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLL2) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLL2) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_spi.o(i.HAL_SPI_DeInit) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAError) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to timer.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to timer.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to timer.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to usart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to usart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_usart.o(i.HAL_USART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f1xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f1xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f1xx_hal_usart.o(i.USART_DMATxAbortCallback) for USART_DMATxAbortCallback
    stm32f1xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f1xx_hal_usart.o(i.USART_DMARxAbortCallback) for USART_DMARxAbortCallback
    stm32f1xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f1xx_hal_usart.o(i.USART_EndTxTransfer) for USART_EndTxTransfer
    stm32f1xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f1xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f1xx_hal_usart.o(i.HAL_USART_DeInit) refers to stm32f1xx_hal_usart.o(i.HAL_USART_MspDeInit) for HAL_USART_MspDeInit
    stm32f1xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f1xx_hal_usart.o(i.USART_TransmitReceive_IT) for USART_TransmitReceive_IT
    stm32f1xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f1xx_hal_usart.o(i.USART_Receive_IT) for USART_Receive_IT
    stm32f1xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f1xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f1xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f1xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f1xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f1xx_hal_usart.o(i.USART_Transmit_IT) for USART_Transmit_IT
    stm32f1xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f1xx_hal_usart.o(i.USART_EndTransmit_IT) for USART_EndTransmit_IT
    stm32f1xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f1xx_hal_usart.o(i.USART_DMAAbortOnError) for USART_DMAAbortOnError
    stm32f1xx_hal_usart.o(i.HAL_USART_Init) refers to stm32f1xx_hal_usart.o(i.HAL_USART_MspInit) for HAL_USART_MspInit
    stm32f1xx_hal_usart.o(i.HAL_USART_Init) refers to stm32f1xx_hal_usart.o(i.USART_SetConfig) for USART_SetConfig
    stm32f1xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32f1xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32f1xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32f1xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f1xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32f1xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32f1xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f1xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32f1xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32f1xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f1xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f1xx_hal_usart.o(i.USART_DMAAbortOnError) refers to stm32f1xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f1xx_hal_usart.o(i.USART_DMAError) refers to stm32f1xx_hal_usart.o(i.USART_EndTxTransfer) for USART_EndTxTransfer
    stm32f1xx_hal_usart.o(i.USART_DMAError) refers to stm32f1xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f1xx_hal_usart.o(i.USART_DMAError) refers to stm32f1xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f1xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32f1xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32f1xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32f1xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32f1xx_hal_usart.o(i.USART_DMARxAbortCallback) refers to stm32f1xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f1xx_hal_usart.o(i.USART_DMARxHalfCplt) refers to stm32f1xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback) for HAL_USART_RxHalfCpltCallback
    stm32f1xx_hal_usart.o(i.USART_DMATransmitCplt) refers to stm32f1xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32f1xx_hal_usart.o(i.USART_DMATxAbortCallback) refers to stm32f1xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f1xx_hal_usart.o(i.USART_DMATxHalfCplt) refers to stm32f1xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback) for HAL_USART_TxHalfCpltCallback
    stm32f1xx_hal_usart.o(i.USART_EndTransmit_IT) refers to stm32f1xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32f1xx_hal_usart.o(i.USART_Receive_IT) refers to stm32f1xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32f1xx_hal_usart.o(i.USART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_usart.o(i.USART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_usart.o(i.USART_TransmitReceive_IT) refers to stm32f1xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32f1xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADD10) for I2C_Master_ADD10
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) for I2C_SlaveReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) for I2C_SlaveTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF) for I2C_SlaveTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_BTF) for I2C_SlaveReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) for I2C_WaitOnSTOPRequestThroughIT
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to sys.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to candataprocess.o(i.InitModuleParams) for InitModuleParams
    main.o(i.main) refers to adc.o(i.ADC_Init) for ADC_Init
    main.o(i.main) refers to timer.o(i.TIM2_Init) for TIM2_Init
    main.o(i.main) refers to timer.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to spi.o(i.SPI1_Init) for SPI1_Init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to candataprocess.o(i.CanRxDataProcess) for CanRxDataProcess
    main.o(i.main) refers to gpio.o(i.GetMarkAddr) for GetMarkAddr
    main.o(i.main) refers to can.o(i.CAN1_Init) for CAN1_Init
    main.o(i.main) refers to can.o(i.CAN2_Init) for CAN2_Init
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to can.o(.data) for Can1RxCpt
    main.o(i.main) refers to timer.o(.data) for Time_1S
    main.o(i.main) refers to can.o(.bss) for Can1RxData
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    delay.o(i.delay_init) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    sys.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    sys.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    sys.o(i.SystemClock_Config) refers to sys.o(i.Error_Handler) for Error_Handler
    sys.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    usart.o(i.HAL_UART_ErrorCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    usart.o(i.HAL_UART_ErrorCallback) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UART_IdleCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    usart.o(i.HAL_UART_IdleCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    usart.o(i.HAL_UART_IdleCallback) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UART_IdleCallback) refers to usart.o(.data) for .data
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.USART2_IRQHandler) refers to usart.o(i.HAL_UART_IdleCallback) for HAL_UART_IdleCallback
    usart.o(i.USART2_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    usart.o(i.USART2_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.Usart2_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.Usart2_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    usart.o(i.Usart2_Init) refers to usart.o(.bss) for .bss
    can.o(i.CAN1_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Init) for HAL_CAN_Init
    can.o(i.CAN1_Init) refers to sys.o(i.Error_Handler) for Error_Handler
    can.o(i.CAN1_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ConfigFilter) for HAL_CAN_ConfigFilter
    can.o(i.CAN1_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Start) for HAL_CAN_Start
    can.o(i.CAN1_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ActivateNotification) for HAL_CAN_ActivateNotification
    can.o(i.CAN1_Init) refers to can.o(.bss) for .bss
    can.o(i.CAN1_RX0_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    can.o(i.CAN1_RX0_IRQHandler) refers to can.o(.bss) for .bss
    can.o(i.CAN2_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Init) for HAL_CAN_Init
    can.o(i.CAN2_Init) refers to sys.o(i.Error_Handler) for Error_Handler
    can.o(i.CAN2_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ConfigFilter) for HAL_CAN_ConfigFilter
    can.o(i.CAN2_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_Start) for HAL_CAN_Start
    can.o(i.CAN2_Init) refers to stm32f1xx_hal_can.o(i.HAL_CAN_ActivateNotification) for HAL_CAN_ActivateNotification
    can.o(i.CAN2_Init) refers to can.o(.bss) for .bss
    can.o(i.CAN2_RX0_IRQHandler) refers to stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler) for HAL_CAN_IRQHandler
    can.o(i.CAN2_RX0_IRQHandler) refers to can.o(.bss) for .bss
    can.o(i.CANxSend) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    can.o(i.CANxSend) refers to stm32f1xx_hal_can.o(i.HAL_CAN_AddTxMessage) for HAL_CAN_AddTxMessage
    can.o(i.CANxSend) refers to stm32f1xx_hal_can.o(i.HAL_CAN_IsTxMessagePending) for HAL_CAN_IsTxMessagePending
    can.o(i.CANxSend) refers to can.o(.data) for .data
    can.o(i.CANxSend) refers to can.o(.bss) for .bss
    can.o(i.HAL_CAN_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    can.o(i.HAL_CAN_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    can.o(i.HAL_CAN_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to stm32f1xx_hal_can.o(i.HAL_CAN_GetRxMessage) for HAL_CAN_GetRxMessage
    can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to sys.o(i.Error_Handler) for Error_Handler
    can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to can.o(.data) for .data
    can.o(i.HAL_CAN_RxFifo0MsgPendingCallback) refers to can.o(.bss) for .bss
    candataprocess.o(i.CanRxDataProcess) refers to candataprocess.o(i.UpdateBitInfo) for UpdateBitInfo
    candataprocess.o(i.CanRxDataProcess) refers to candataprocess.o(i.ProcessBitQuery) for ProcessBitQuery
    candataprocess.o(i.CanRxDataProcess) refers to candataprocess.o(i.SetProductInfo) for SetProductInfo
    candataprocess.o(i.CanRxDataProcess) refers to candataprocess.o(i.SendCanResponse) for SendCanResponse
    candataprocess.o(i.CanRxDataProcess) refers to candataprocess.o(i.ProcessProductInfoQuery) for ProcessProductInfoQuery
    candataprocess.o(i.CanRxDataProcess) refers to candataprocess.o(i.ProcessParamQuery) for ProcessParamQuery
    candataprocess.o(i.CanRxDataProcess) refers to candataprocess.o(i.ProcessParamSet) for ProcessParamSet
    candataprocess.o(i.CanRxDataProcess) refers to candataprocess.o(.bss) for .bss
    candataprocess.o(i.CanRxDataProcess) refers to candataprocess.o(.data) for .data
    candataprocess.o(i.InitModuleParams) refers to stm32flash.o(i.Stm32Flash_Read) for Stm32Flash_Read
    candataprocess.o(i.InitModuleParams) refers to candataprocess.o(.bss) for .bss
    candataprocess.o(i.ProcessBitQuery) refers to rt_memclr.o(.text) for __aeabi_memclr
    candataprocess.o(i.ProcessBitQuery) refers to candataprocess.o(i.SendCanResponse) for SendCanResponse
    candataprocess.o(i.ProcessBitQuery) refers to candataprocess.o(.bss) for .bss
    candataprocess.o(i.ProcessBitQuery) refers to candataprocess.o(.data) for .data
    candataprocess.o(i.ProcessParamQuery) refers to rt_memclr.o(.text) for __aeabi_memclr
    candataprocess.o(i.ProcessParamQuery) refers to aeabi_memset.o(.text) for __aeabi_memset
    candataprocess.o(i.ProcessParamQuery) refers to candataprocess.o(i.SendCanResponse) for SendCanResponse
    candataprocess.o(i.ProcessParamQuery) refers to candataprocess.o(.bss) for .bss
    candataprocess.o(i.ProcessParamQuery) refers to candataprocess.o(.data) for .data
    candataprocess.o(i.ProcessParamSet) refers to fpga_ctrl.o(i.FPGA_ProcessChannelSet) for FPGA_ProcessChannelSet
    candataprocess.o(i.ProcessParamSet) refers to candataprocess.o(.bss) for .bss
    candataprocess.o(i.ProcessProductInfoQuery) refers to rt_memclr.o(.text) for __aeabi_memclr
    candataprocess.o(i.ProcessProductInfoQuery) refers to candataprocess.o(i.SendCanResponse) for SendCanResponse
    candataprocess.o(i.ProcessProductInfoQuery) refers to candataprocess.o(.bss) for .bss
    candataprocess.o(i.ProcessProductInfoQuery) refers to candataprocess.o(.data) for .data
    candataprocess.o(i.SendCanResponse) refers to can.o(i.CANxSend) for CANxSend
    candataprocess.o(i.SendCanResponse) refers to candataprocess.o(.data) for .data
    candataprocess.o(i.SendCanResponse) refers to candataprocess.o(.bss) for .bss
    candataprocess.o(i.SetProductInfo) refers to stm32flash.o(i.STMFLASH_Write) for STMFLASH_Write
    candataprocess.o(i.SetProductInfo) refers to candataprocess.o(.bss) for .bss
    candataprocess.o(i.UpdateBitInfo) refers to fpga_ctrl.o(i.FPGA_ReadParams) for FPGA_ReadParams
    candataprocess.o(i.UpdateBitInfo) refers to fpga_ctrl.o(i.FPGA_GetTemperature) for FPGA_GetTemperature
    candataprocess.o(i.UpdateBitInfo) refers to fpga_ctrl.o(i.FPGA_GetLockStatus) for FPGA_GetLockStatus
    candataprocess.o(i.UpdateBitInfo) refers to candataprocess.o(.bss) for .bss
    spi.o(i.HAL_SPI_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.HAL_SPI_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    spi.o(i.HAL_SPI_MspInit) refers to delay.o(i.delay_us) for delay_us
    spi.o(i.SPI1_Init) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.SPI1_Init) refers to sys.o(i.Error_Handler) for Error_Handler
    spi.o(i.SPI1_Init) refers to delay.o(i.delay_ms) for delay_ms
    spi.o(i.SPI1_Init) refers to spi.o(.bss) for .bss
    spi.o(i.SPI1_SendData) refers to delay.o(i.delay_us) for delay_us
    spi.o(i.SPI1_SendData) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit) for HAL_SPI_Transmit
    spi.o(i.SPI1_SendData) refers to sys.o(i.Error_Handler) for Error_Handler
    spi.o(i.SPI1_SendData) refers to spi.o(.bss) for .bss
    stm32flash.o(i.STMFLASH_Write) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock) for HAL_FLASH_Unlock
    stm32flash.o(i.STMFLASH_Write) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) for HAL_FLASHEx_Erase
    stm32flash.o(i.STMFLASH_Write) refers to sys.o(i.Error_Handler) for Error_Handler
    stm32flash.o(i.STMFLASH_Write) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) for HAL_FLASH_Program
    stm32flash.o(i.STMFLASH_Write) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock) for HAL_FLASH_Lock
    adc.o(i.ADC_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    adc.o(i.ADC_Init) refers to stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    adc.o(i.ADC_Init) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.ADC_Init) refers to stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) for HAL_ADCEx_Calibration_Start
    adc.o(i.ADC_Init) refers to adc.o(.bss) for .bss
    adc.o(i.GetTempValeue) refers to adc.o(i.Get_Adc_Average) for Get_Adc_Average
    adc.o(i.GetTempValeue) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    adc.o(i.GetTempValeue) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    adc.o(i.GetTempValeue) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    adc.o(i.GetTempValeue) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    adc.o(i.GetTempValeue) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    adc.o(i.GetTempValeue) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    adc.o(i.GetTempValeue) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    adc.o(i.GetTempValeue) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    adc.o(i.GetTempValeue) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    adc.o(i.GetVoltage5_5V) refers to adc.o(i.Get_Adc_Average) for Get_Adc_Average
    adc.o(i.GetVoltage5_5V) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    adc.o(i.GetVoltage5_5V) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    adc.o(i.GetVoltage5_5V) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    adc.o(i.GetVoltage5_5V) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    adc.o(i.GetVoltage5_5V) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    adc.o(i.GetVoltage5_5V) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    adc.o(i.Get_Adc) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.Get_Adc) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_Start) for HAL_ADC_Start
    adc.o(i.Get_Adc) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion) for HAL_ADC_PollForConversion
    adc.o(i.Get_Adc) refers to stm32f1xx_hal_adc.o(i.HAL_ADC_GetValue) for HAL_ADC_GetValue
    adc.o(i.Get_Adc) refers to adc.o(.bss) for .bss
    adc.o(i.Get_Adc_Average) refers to adc.o(i.Get_Adc) for Get_Adc
    adc.o(i.Get_Adc_Average) refers to delay.o(i.delay_us) for delay_us
    adc.o(i.HAL_ADC_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.GpioInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    fpga_ctrl.o(i.FPGA_GetLockStatus) refers to fpga_ctrl.o(.data) for .data
    fpga_ctrl.o(i.FPGA_GetTemperature) refers to fpga_ctrl.o(.data) for .data
    fpga_ctrl.o(i.FPGA_ProcessChannelSet) refers to fpga_ctrl.o(i.FPGA_SetChannelParams) for FPGA_SetChannelParams
    fpga_ctrl.o(i.FPGA_ReadParams) refers to fpga_ctrl.o(i.FPGA_SendFrame) for FPGA_SendFrame
    fpga_ctrl.o(i.FPGA_ReadParams) refers to fpga_ctrl.o(.data) for .data
    fpga_ctrl.o(i.FPGA_SendFrame) refers to spi.o(.bss) for hspi1
    fpga_ctrl.o(i.FPGA_SetChannelParams) refers to fpga_ctrl.o(i.ConvertLowFrequency) for ConvertLowFrequency
    fpga_ctrl.o(i.FPGA_SetChannelParams) refers to fpga_ctrl.o(i.FPGA_SendFrame) for FPGA_SendFrame
    timer.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    timer.o(i.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to timer.o(.bss) for .bss
    timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to timer.o(.data) for .data
    timer.o(i.LED_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    timer.o(i.TIM2_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    timer.o(i.TIM2_IRQHandler) refers to timer.o(.bss) for .bss
    timer.o(i.TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    timer.o(i.TIM2_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    timer.o(i.TIM2_Init) refers to timer.o(.bss) for .bss
    ds18b20.o(i.DS18B20_Check) refers to delay.o(i.delay_us) for delay_us
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Start) for DS18B20_Start
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Rst) for DS18B20_Rst
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Check) for DS18B20_Check
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Write_Byte) for DS18B20_Write_Byte
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Read_Byte) for DS18B20_Read_Byte
    ds18b20.o(i.DS18B20_Get_Temp) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    ds18b20.o(i.DS18B20_Get_Temp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ds18b20.o(i.DS18B20_Get_Temp) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    ds18b20.o(i.DS18B20_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ds18b20.o(i.DS18B20_Init) refers to ds18b20.o(i.DS18B20_Rst) for DS18B20_Rst
    ds18b20.o(i.DS18B20_Init) refers to ds18b20.o(i.DS18B20_Check) for DS18B20_Check
    ds18b20.o(i.DS18B20_Read_Bit) refers to delay.o(i.delay_us) for delay_us
    ds18b20.o(i.DS18B20_Read_Byte) refers to ds18b20.o(i.DS18B20_Read_Bit) for DS18B20_Read_Bit
    ds18b20.o(i.DS18B20_Rst) refers to delay.o(i.delay_us) for delay_us
    ds18b20.o(i.DS18B20_Start) refers to ds18b20.o(i.DS18B20_Rst) for DS18B20_Rst
    ds18b20.o(i.DS18B20_Start) refers to ds18b20.o(i.DS18B20_Check) for DS18B20_Check
    ds18b20.o(i.DS18B20_Start) refers to ds18b20.o(i.DS18B20_Write_Byte) for DS18B20_Write_Byte
    ds18b20.o(i.DS18B20_Write_Byte) refers to delay.o(i.delay_us) for delay_us
    i2c.o(i.I2C_SCL) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    i2c.o(i.I2C_SDA) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    i2c.o(i.READ_SDA) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    i2c.o(i.SDA_IN) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.SDA_OUT) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.Soft_I2C_Ack) refers to i2c.o(i.I2C_SCL) for I2C_SCL
    i2c.o(i.Soft_I2C_Ack) refers to i2c.o(i.SDA_OUT) for SDA_OUT
    i2c.o(i.Soft_I2C_Ack) refers to i2c.o(i.I2C_SDA) for I2C_SDA
    i2c.o(i.Soft_I2C_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.Soft_I2C_Init) refers to i2c.o(i.I2C_SCL) for I2C_SCL
    i2c.o(i.Soft_I2C_Init) refers to i2c.o(i.I2C_SDA) for I2C_SDA
    i2c.o(i.Soft_I2C_Master_Receive) refers to i2c.o(i.Soft_I2C_Start) for Soft_I2C_Start
    i2c.o(i.Soft_I2C_Master_Receive) refers to i2c.o(i.Soft_I2C_SendByte) for Soft_I2C_SendByte
    i2c.o(i.Soft_I2C_Master_Receive) refers to i2c.o(i.Soft_I2C_WaitAck) for Soft_I2C_WaitAck
    i2c.o(i.Soft_I2C_Master_Receive) refers to i2c.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    i2c.o(i.Soft_I2C_Master_Receive) refers to i2c.o(i.Soft_I2C_ReadByte) for Soft_I2C_ReadByte
    i2c.o(i.Soft_I2C_Master_Transmit) refers to i2c.o(i.Soft_I2C_Start) for Soft_I2C_Start
    i2c.o(i.Soft_I2C_Master_Transmit) refers to i2c.o(i.Soft_I2C_SendByte) for Soft_I2C_SendByte
    i2c.o(i.Soft_I2C_Master_Transmit) refers to i2c.o(i.Soft_I2C_WaitAck) for Soft_I2C_WaitAck
    i2c.o(i.Soft_I2C_Master_Transmit) refers to i2c.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    i2c.o(i.Soft_I2C_Mem_Read) refers to i2c.o(i.Soft_I2C_Start) for Soft_I2C_Start
    i2c.o(i.Soft_I2C_Mem_Read) refers to i2c.o(i.Soft_I2C_SendByte) for Soft_I2C_SendByte
    i2c.o(i.Soft_I2C_Mem_Read) refers to i2c.o(i.Soft_I2C_WaitAck) for Soft_I2C_WaitAck
    i2c.o(i.Soft_I2C_Mem_Read) refers to i2c.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    i2c.o(i.Soft_I2C_Mem_Read) refers to i2c.o(i.Soft_I2C_ReadByte) for Soft_I2C_ReadByte
    i2c.o(i.Soft_I2C_Mem_Write) refers to i2c.o(i.Soft_I2C_Start) for Soft_I2C_Start
    i2c.o(i.Soft_I2C_Mem_Write) refers to i2c.o(i.Soft_I2C_SendByte) for Soft_I2C_SendByte
    i2c.o(i.Soft_I2C_Mem_Write) refers to i2c.o(i.Soft_I2C_WaitAck) for Soft_I2C_WaitAck
    i2c.o(i.Soft_I2C_Mem_Write) refers to i2c.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    i2c.o(i.Soft_I2C_NAck) refers to i2c.o(i.I2C_SCL) for I2C_SCL
    i2c.o(i.Soft_I2C_NAck) refers to i2c.o(i.SDA_OUT) for SDA_OUT
    i2c.o(i.Soft_I2C_NAck) refers to i2c.o(i.I2C_SDA) for I2C_SDA
    i2c.o(i.Soft_I2C_ReadByte) refers to i2c.o(i.SDA_IN) for SDA_IN
    i2c.o(i.Soft_I2C_ReadByte) refers to i2c.o(i.I2C_SCL) for I2C_SCL
    i2c.o(i.Soft_I2C_ReadByte) refers to i2c.o(i.READ_SDA) for READ_SDA
    i2c.o(i.Soft_I2C_ReadByte) refers to i2c.o(i.Soft_I2C_Ack) for Soft_I2C_Ack
    i2c.o(i.Soft_I2C_ReadByte) refers to i2c.o(i.Soft_I2C_NAck) for Soft_I2C_NAck
    i2c.o(i.Soft_I2C_SendByte) refers to i2c.o(i.SDA_OUT) for SDA_OUT
    i2c.o(i.Soft_I2C_SendByte) refers to i2c.o(i.I2C_SCL) for I2C_SCL
    i2c.o(i.Soft_I2C_SendByte) refers to i2c.o(i.I2C_SDA) for I2C_SDA
    i2c.o(i.Soft_I2C_Start) refers to i2c.o(i.SDA_OUT) for SDA_OUT
    i2c.o(i.Soft_I2C_Start) refers to i2c.o(i.I2C_SDA) for I2C_SDA
    i2c.o(i.Soft_I2C_Start) refers to i2c.o(i.I2C_SCL) for I2C_SCL
    i2c.o(i.Soft_I2C_Stop) refers to i2c.o(i.SDA_OUT) for SDA_OUT
    i2c.o(i.Soft_I2C_Stop) refers to i2c.o(i.I2C_SCL) for I2C_SCL
    i2c.o(i.Soft_I2C_Stop) refers to i2c.o(i.I2C_SDA) for I2C_SDA
    i2c.o(i.Soft_I2C_WaitAck) refers to i2c.o(i.SDA_IN) for SDA_IN
    i2c.o(i.Soft_I2C_WaitAck) refers to i2c.o(i.I2C_SDA) for I2C_SDA
    i2c.o(i.Soft_I2C_WaitAck) refers to i2c.o(i.I2C_SCL) for I2C_SCL
    i2c.o(i.Soft_I2C_WaitAck) refers to i2c.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    i2c.o(i.Soft_I2C_WaitAck) refers to i2c.o(i.READ_SDA) for READ_SDA
    ltc2945.o(i.LTC2945_BulkRead) refers to i2c.o(i.Soft_I2C_Start) for Soft_I2C_Start
    ltc2945.o(i.LTC2945_BulkRead) refers to i2c.o(i.Soft_I2C_SendByte) for Soft_I2C_SendByte
    ltc2945.o(i.LTC2945_BulkRead) refers to i2c.o(i.Soft_I2C_WaitAck) for Soft_I2C_WaitAck
    ltc2945.o(i.LTC2945_BulkRead) refers to i2c.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    ltc2945.o(i.LTC2945_BulkRead) refers to i2c.o(i.Soft_I2C_ReadByte) for Soft_I2C_ReadByte
    ltc2945.o(i.LTC2945_ConvertAdinToVolts) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    ltc2945.o(i.LTC2945_ConvertAdinToVolts) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    ltc2945.o(i.LTC2945_ConvertPowerToWatts) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    ltc2945.o(i.LTC2945_ConvertPowerToWatts) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    ltc2945.o(i.LTC2945_ConvertPowerToWatts) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    ltc2945.o(i.LTC2945_ConvertSenseToAmps) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    ltc2945.o(i.LTC2945_ConvertSenseToAmps) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    ltc2945.o(i.LTC2945_ConvertSenseToAmps) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    ltc2945.o(i.LTC2945_ConvertVinToVolts) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    ltc2945.o(i.LTC2945_ConvertVinToVolts) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    ltc2945.o(i.LTC2945_Init) refers to i2c.o(i.Soft_I2C_Init) for Soft_I2C_Init
    ltc2945.o(i.LTC2945_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    ltc2945.o(i.LTC2945_Init) refers to ltc2945.o(i.LTC2945_ReadRegister) for LTC2945_ReadRegister
    ltc2945.o(i.LTC2945_Init) refers to ltc2945.o(i.LTC2945_WriteRegister) for LTC2945_WriteRegister
    ltc2945.o(i.LTC2945_ReadAdinVoltage) refers to ltc2945.o(i.LTC2945_BulkRead) for LTC2945_BulkRead
    ltc2945.o(i.LTC2945_ReadAllData) refers to ltc2945.o(i.LTC2945_ReadSenseVoltage) for LTC2945_ReadSenseVoltage
    ltc2945.o(i.LTC2945_ReadAllData) refers to ltc2945.o(i.LTC2945_ReadPower) for LTC2945_ReadPower
    ltc2945.o(i.LTC2945_ReadAllData) refers to ltc2945.o(i.LTC2945_ReadVinVoltage) for LTC2945_ReadVinVoltage
    ltc2945.o(i.LTC2945_ReadAllData) refers to ltc2945.o(i.LTC2945_ReadAdinVoltage) for LTC2945_ReadAdinVoltage
    ltc2945.o(i.LTC2945_ReadPower) refers to ltc2945.o(i.LTC2945_BulkRead) for LTC2945_BulkRead
    ltc2945.o(i.LTC2945_ReadRegister) refers to i2c.o(i.Soft_I2C_Start) for Soft_I2C_Start
    ltc2945.o(i.LTC2945_ReadRegister) refers to i2c.o(i.Soft_I2C_SendByte) for Soft_I2C_SendByte
    ltc2945.o(i.LTC2945_ReadRegister) refers to i2c.o(i.Soft_I2C_WaitAck) for Soft_I2C_WaitAck
    ltc2945.o(i.LTC2945_ReadRegister) refers to i2c.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    ltc2945.o(i.LTC2945_ReadRegister) refers to i2c.o(i.Soft_I2C_ReadByte) for Soft_I2C_ReadByte
    ltc2945.o(i.LTC2945_ReadSenseVoltage) refers to ltc2945.o(i.LTC2945_BulkRead) for LTC2945_BulkRead
    ltc2945.o(i.LTC2945_ReadVinVoltage) refers to ltc2945.o(i.LTC2945_BulkRead) for LTC2945_BulkRead
    ltc2945.o(i.LTC2945_WriteControlRegisters) refers to i2c.o(i.Soft_I2C_Start) for Soft_I2C_Start
    ltc2945.o(i.LTC2945_WriteControlRegisters) refers to i2c.o(i.Soft_I2C_SendByte) for Soft_I2C_SendByte
    ltc2945.o(i.LTC2945_WriteControlRegisters) refers to i2c.o(i.Soft_I2C_WaitAck) for Soft_I2C_WaitAck
    ltc2945.o(i.LTC2945_WriteControlRegisters) refers to i2c.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    ltc2945.o(i.LTC2945_WriteCurrentThreshold) refers to i2c.o(i.Soft_I2C_Mem_Write) for Soft_I2C_Mem_Write
    ltc2945.o(i.LTC2945_WriteRegister) refers to i2c.o(i.Soft_I2C_Start) for Soft_I2C_Start
    ltc2945.o(i.LTC2945_WriteRegister) refers to i2c.o(i.Soft_I2C_SendByte) for Soft_I2C_SendByte
    ltc2945.o(i.LTC2945_WriteRegister) refers to i2c.o(i.Soft_I2C_WaitAck) for Soft_I2C_WaitAck
    ltc2945.o(i.LTC2945_WriteRegister) refers to i2c.o(i.Soft_I2C_Stop) for Soft_I2C_Stop
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f107xc.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (168 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_adc.o(i.ADC_DMAConvCplt), (78 bytes).
    Removing stm32f1xx_hal_adc.o(i.ADC_DMAError), (26 bytes).
    Removing stm32f1xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (120 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_ConfigChannel), (264 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_DeInit), (228 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_IRQHandler), (230 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_PollForConversion), (332 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_PollForEvent), (94 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Start), (208 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Start_DMA), (288 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Start_IT), (220 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Stop), (52 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (90 bytes).
    Removing stm32f1xx_hal_adc.o(i.HAL_ADC_Stop_IT), (62 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (492 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (30 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (320 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (172 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (180 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (78 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (88 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (112 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (32 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (244 bytes).
    Removing stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (124 bytes).
    Removing stm32f1xx_hal_can.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_can.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_can.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_AbortTxRequest), (70 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_DeInit), (44 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_DeactivateNotification), (36 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetError), (4 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetRxFifoFillLevel), (38 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetState), (36 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetTxMailboxesFreeLevel), (44 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_GetTxTimestamp), (40 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_IsSleepActive), (28 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_MspInit), (2 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_RequestSleep), (38 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_ResetError), (34 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0MsgPendingCallback), (2 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_Stop), (98 bytes).
    Removing stm32f1xx_hal_can.o(i.HAL_CAN_WakeUp), (84 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (86 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_crc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_crc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_crc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (132 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (672 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (128 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (1268 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (76 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (86 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (180 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (276 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (180 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (180 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_ProgramData), (64 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (68 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (28 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (124 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (232 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (36 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (14 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (304 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (184 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq), (32 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLL2), (76 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (68 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLL2), (160 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (152 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (68 bytes).
    Removing stm32f1xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Abort), (292 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT), (300 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAStop), (68 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler), (240 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive), (346 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (244 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT), (180 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit), (394 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive), (484 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (312 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (164 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (216 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (152 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR), (88 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR), (28 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (152 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR), (76 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR), (128 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt), (108 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback), (98 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (90 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback), (112 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction), (112 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction), (36 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout), (188 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (72 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (180 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (226 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (226 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (424 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (424 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (126 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (126 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (64 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (168 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (500 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (208 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (170 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (234 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (210 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (36 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (180 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (72 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (74 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (240 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (480 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (300 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (114 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (72 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init), (74 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (184 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (432 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (116 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (234 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (64 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (210 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (72 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init), (74 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start), (184 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (432 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (116 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (62 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (62 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig), (96 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig), (96 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig), (72 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (134 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI3_SetConfig), (50 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI4_SetConfig), (56 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (64 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (210 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (212 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (156 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (180 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (384 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (236 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (106 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (166 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (98 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (118 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (100 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (180 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (384 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (236 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (106 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (166 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (94 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (260 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (82 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (82 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (162 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (106 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (128 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (78 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (96 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (196 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (102 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (94 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Init), (100 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive), (202 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit), (190 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (140 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (46 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_SetConfig), (184 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (100 bytes).
    Removing stm32f1xx_hal_usart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_usart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_usart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_Abort), (100 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_Abort_IT), (176 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_DMAPause), (38 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_DMAResume), (38 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_DMAStop), (92 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_DeInit), (46 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_GetError), (4 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_GetState), (6 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_IRQHandler), (328 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_Init), (86 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_Receive), (220 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_Receive_DMA), (204 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_Receive_IT), (84 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_Transmit), (190 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive), (314 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA), (224 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT), (106 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_Transmit_DMA), (140 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_Transmit_IT), (66 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_DMAAbortOnError), (16 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_DMAError), (80 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_DMAReceiveCplt), (110 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_DMARxAbortCallback), (40 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_DMARxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_DMATransmitCplt), (62 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_DMATxAbortCallback), (40 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_EndRxTransfer), (28 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_EndTransmit_IT), (36 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_EndTxTransfer), (18 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_Receive_IT), (150 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_SetConfig), (312 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_TransmitReceive_IT), (226 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_Transmit_IT), (94 bytes).
    Removing stm32f1xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (62 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (412 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Init), (408 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (376 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (94 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (720 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (204 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (532 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (316 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (420 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (328 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (728 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (456 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (232 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (324 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (396 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (216 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (328 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (128 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (368 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (120 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (368 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (120 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (356 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (128 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (28 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAError), (56 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt), (272 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_ITError), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed), (46 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (256 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (210 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead), (276 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite), (176 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (138 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (190 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_ADD10), (42 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR), (408 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_SB), (160 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (182 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead), (320 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_BTF), (28 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE), (68 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF), (28 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE), (68 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR), (72 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_AF), (132 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout), (78 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (122 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (108 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (74 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT), (60 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout), (78 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.data), (6 bytes).
    Removing main.o(.data), (6 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspInit), (184 bytes).
    Removing usart.o(i.Usart2_Init), (76 bytes).
    Removing usart.o(i.fgetc), (12 bytes).
    Removing usart.o(i.fputc), (24 bytes).
    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing can.o(.rrx_text), (6 bytes).
    Removing candataprocess.o(.rev16_text), (4 bytes).
    Removing candataprocess.o(.revsh_text), (4 bytes).
    Removing candataprocess.o(.rrx_text), (6 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.SPI1_SendData), (84 bytes).
    Removing stm32flash.o(.rev16_text), (4 bytes).
    Removing stm32flash.o(.revsh_text), (4 bytes).
    Removing stm32flash.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.GetTempValeue), (100 bytes).
    Removing adc.o(i.GetVoltage5_5V), (68 bytes).
    Removing adc.o(i.Get_Adc), (48 bytes).
    Removing adc.o(i.Get_Adc_Average), (46 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing gpio.o(i.GpioInit), (128 bytes).
    Removing fpga_ctrl.o(.rev16_text), (4 bytes).
    Removing fpga_ctrl.o(.revsh_text), (4 bytes).
    Removing fpga_ctrl.o(.rrx_text), (6 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing timer.o(.rrx_text), (6 bytes).
    Removing ds18b20.o(.rev16_text), (4 bytes).
    Removing ds18b20.o(.revsh_text), (4 bytes).
    Removing ds18b20.o(.rrx_text), (6 bytes).
    Removing ds18b20.o(i.DS18B20_Check), (100 bytes).
    Removing ds18b20.o(i.DS18B20_Get_Temp), (62 bytes).
    Removing ds18b20.o(i.DS18B20_Init), (68 bytes).
    Removing ds18b20.o(i.DS18B20_Read_Bit), (88 bytes).
    Removing ds18b20.o(i.DS18B20_Read_Byte), (30 bytes).
    Removing ds18b20.o(i.DS18B20_Rst), (56 bytes).
    Removing ds18b20.o(i.DS18B20_Start), (26 bytes).
    Removing ds18b20.o(i.DS18B20_Write_Byte), (104 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.I2C_SCL), (24 bytes).
    Removing i2c.o(i.I2C_SDA), (24 bytes).
    Removing i2c.o(i.READ_SDA), (16 bytes).
    Removing i2c.o(i.SDA_IN), (32 bytes).
    Removing i2c.o(i.SDA_OUT), (36 bytes).
    Removing i2c.o(i.Soft_I2C_Ack), (72 bytes).
    Removing i2c.o(i.Soft_I2C_Init), (108 bytes).
    Removing i2c.o(i.Soft_I2C_Master_Receive), (86 bytes).
    Removing i2c.o(i.Soft_I2C_Master_Transmit), (74 bytes).
    Removing i2c.o(i.Soft_I2C_Mem_Read), (134 bytes).
    Removing i2c.o(i.Soft_I2C_Mem_Write), (100 bytes).
    Removing i2c.o(i.Soft_I2C_NAck), (72 bytes).
    Removing i2c.o(i.Soft_I2C_ReadByte), (108 bytes).
    Removing i2c.o(i.Soft_I2C_SendByte), (102 bytes).
    Removing i2c.o(i.Soft_I2C_Start), (72 bytes).
    Removing i2c.o(i.Soft_I2C_Stop), (86 bytes).
    Removing i2c.o(i.Soft_I2C_WaitAck), (92 bytes).
    Removing ltc2945.o(.rev16_text), (4 bytes).
    Removing ltc2945.o(.revsh_text), (4 bytes).
    Removing ltc2945.o(.rrx_text), (6 bytes).
    Removing ltc2945.o(i.LTC2945_BulkRead), (118 bytes).
    Removing ltc2945.o(i.LTC2945_ConvertAdinToVolts), (20 bytes).
    Removing ltc2945.o(i.LTC2945_ConvertPowerToWatts), (32 bytes).
    Removing ltc2945.o(i.LTC2945_ConvertSenseToAmps), (28 bytes).
    Removing ltc2945.o(i.LTC2945_ConvertVinToVolts), (20 bytes).
    Removing ltc2945.o(i.LTC2945_Init), (68 bytes).
    Removing ltc2945.o(i.LTC2945_ReadAdinVoltage), (38 bytes).
    Removing ltc2945.o(i.LTC2945_ReadAllData), (60 bytes).
    Removing ltc2945.o(i.LTC2945_ReadPower), (44 bytes).
    Removing ltc2945.o(i.LTC2945_ReadRegister), (90 bytes).
    Removing ltc2945.o(i.LTC2945_ReadSenseVoltage), (38 bytes).
    Removing ltc2945.o(i.LTC2945_ReadVinVoltage), (38 bytes).
    Removing ltc2945.o(i.LTC2945_WriteControlRegisters), (98 bytes).
    Removing ltc2945.o(i.LTC2945_WriteCurrentThreshold), (40 bytes).
    Removing ltc2945.o(i.LTC2945_WriteRegister), (78 bytes).

709 unused section(s) (total 59732 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ..\Bsp\Src\CanDataProcess.c              0x00000000   Number         0  candataprocess.o ABSOLUTE
    ..\Bsp\Src\STM32Flash.c                  0x00000000   Number         0  stm32flash.o ABSOLUTE
    ..\Bsp\Src\adc.c                         0x00000000   Number         0  adc.o ABSOLUTE
    ..\Bsp\Src\can.c                         0x00000000   Number         0  can.o ABSOLUTE
    ..\Bsp\Src\delay.c                       0x00000000   Number         0  delay.o ABSOLUTE
    ..\Bsp\Src\ds18b20.c                     0x00000000   Number         0  ds18b20.o ABSOLUTE
    ..\Bsp\Src\fpga_ctrl.c                   0x00000000   Number         0  fpga_ctrl.o ABSOLUTE
    ..\Bsp\Src\gpio.c                        0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Bsp\Src\i2c.c                         0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Bsp\Src\ltc2945.c                     0x00000000   Number         0  ltc2945.o ABSOLUTE
    ..\Bsp\Src\spi.c                         0x00000000   Number         0  spi.o ABSOLUTE
    ..\Bsp\Src\sys.c                         0x00000000   Number         0  sys.o ABSOLUTE
    ..\Bsp\Src\timer.c                       0x00000000   Number         0  timer.o ABSOLUTE
    ..\Bsp\Src\usart.c                       0x00000000   Number         0  usart.o ABSOLUTE
    ..\Libraries\CMSIS\Device\ST\STM32F1xx\Source\Templates\arm\startup_stm32f107xc.s 0x00000000   Number         0  startup_stm32f107xc.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_adc.c 0x00000000   Number         0  stm32f1xx_hal_adc.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_adc_ex.c 0x00000000   Number         0  stm32f1xx_hal_adc_ex.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_can.c 0x00000000   Number         0  stm32f1xx_hal_can.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_crc.c 0x00000000   Number         0  stm32f1xx_hal_crc.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dac.c 0x00000000   Number         0  stm32f1xx_hal_dac.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dac_ex.c 0x00000000   Number         0  stm32f1xx_hal_dac_ex.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c 0x00000000   Number         0  stm32f1xx_hal_spi.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\Libraries\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_usart.c 0x00000000   Number         0  stm32f1xx_hal_usart.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\stm32f1xx_it.c                   0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\User\system_stm32f1xx.c               0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\\Bsp\\Src\\CanDataProcess.c           0x00000000   Number         0  candataprocess.o ABSOLUTE
    ..\\Bsp\\Src\\STM32Flash.c               0x00000000   Number         0  stm32flash.o ABSOLUTE
    ..\\Bsp\\Src\\adc.c                      0x00000000   Number         0  adc.o ABSOLUTE
    ..\\Bsp\\Src\\can.c                      0x00000000   Number         0  can.o ABSOLUTE
    ..\\Bsp\\Src\\delay.c                    0x00000000   Number         0  delay.o ABSOLUTE
    ..\\Bsp\\Src\\ds18b20.c                  0x00000000   Number         0  ds18b20.o ABSOLUTE
    ..\\Bsp\\Src\\fpga_ctrl.c                0x00000000   Number         0  fpga_ctrl.o ABSOLUTE
    ..\\Bsp\\Src\\gpio.c                     0x00000000   Number         0  gpio.o ABSOLUTE
    ..\\Bsp\\Src\\i2c.c                      0x00000000   Number         0  i2c.o ABSOLUTE
    ..\\Bsp\\Src\\ltc2945.c                  0x00000000   Number         0  ltc2945.o ABSOLUTE
    ..\\Bsp\\Src\\spi.c                      0x00000000   Number         0  spi.o ABSOLUTE
    ..\\Bsp\\Src\\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\\Bsp\\Src\\timer.c                    0x00000000   Number         0  timer.o ABSOLUTE
    ..\\Bsp\\Src\\usart.c                    0x00000000   Number         0  usart.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_adc.c 0x00000000   Number         0  stm32f1xx_hal_adc.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_adc_ex.c 0x00000000   Number         0  stm32f1xx_hal_adc_ex.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_can.c 0x00000000   Number         0  stm32f1xx_hal_can.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_crc.c 0x00000000   Number         0  stm32f1xx_hal_crc.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dac.c 0x00000000   Number         0  stm32f1xx_hal_dac.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dac_ex.c 0x00000000   Number         0  stm32f1xx_hal_dac_ex.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_spi.c 0x00000000   Number         0  stm32f1xx_hal_spi.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\\Libraries\\STM32F1xx_HAL_Driver\\Src\\stm32f1xx_hal_usart.c 0x00000000   Number         0  stm32f1xx_hal_usart.o ABSOLUTE
    ..\\User\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\User\\stm32f1xx_it.c                 0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\\User\\system_stm32f1xx.c             0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      336  startup_stm32f107xc.o(RESET)
    !!!main                                  0x08000150   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000158   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800018c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001a8   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001c4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001c6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001c6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001c8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001ca   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001ca   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080001ca   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001ca   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080001ca   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080001ca   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080001ca   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080001ca   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080001cc   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001cc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001cc   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001d2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001d2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001d6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001d6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001de   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001e0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001e0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001e4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001ec   Section       64  startup_stm32f107xc.o(.text)
    .text                                    0x0800022c   Section      238  lludivv7m.o(.text)
    .text                                    0x0800031a   Section       38  llushr.o(.text)
    .text                                    0x08000340   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080003ca   Section       16  aeabi_memset.o(.text)
    .text                                    0x080003da   Section       68  rt_memclr.o(.text)
    .text                                    0x0800041e   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800046c   Section        0  heapauxi.o(.text)
    .text                                    0x08000472   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080004d6   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000520   Section        0  exit.o(.text)
    .text                                    0x08000534   Section        8  libspace.o(.text)
    .text                                    0x0800053c   Section        0  sys_exit.o(.text)
    .text                                    0x08000548   Section        2  use_no_semi.o(.text)
    .text                                    0x0800054a   Section        0  indicate_semi.o(.text)
    i.ADC_ConversionStop_Disable             0x0800054a   Section        0  stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable)
    i.ADC_Enable                             0x080005a0   Section        0  stm32f1xx_hal_adc.o(i.ADC_Enable)
    i.ADC_Init                               0x08000620   Section        0  adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x0800066c   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.CAN1_Init                              0x08000670   Section        0  can.o(i.CAN1_Init)
    i.CAN1_RX0_IRQHandler                    0x08000718   Section        0  can.o(i.CAN1_RX0_IRQHandler)
    i.CAN2_Init                              0x08000724   Section        0  can.o(i.CAN2_Init)
    i.CAN2_RX0_IRQHandler                    0x080007cc   Section        0  can.o(i.CAN2_RX0_IRQHandler)
    i.CANxSend                               0x080007d8   Section        0  can.o(i.CANxSend)
    i.CanRxDataProcess                       0x08000874   Section        0  candataprocess.o(i.CanRxDataProcess)
    i.ConvertLowFrequency                    0x08000934   Section        0  fpga_ctrl.o(i.ConvertLowFrequency)
    ConvertLowFrequency                      0x08000935   Thumb Code     6  fpga_ctrl.o(i.ConvertLowFrequency)
    i.DMA_SetConfig                          0x08000940   Section        0  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08000941   Thumb Code    44  stm32f1xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x0800096c   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x0800096e   Section        0  sys.o(i.Error_Handler)
    i.FLASH_MassErase                        0x08000978   Section        0  stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase)
    FLASH_MassErase                          0x08000979   Thumb Code    26  stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase)
    i.FLASH_PageErase                        0x0800099c   Section        0  stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase)
    i.FLASH_Program_HalfWord                 0x080009c0   Section        0  stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord)
    FLASH_Program_HalfWord                   0x080009c1   Thumb Code    20  stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord)
    i.FLASH_SetErrorCode                     0x080009dc   Section        0  stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode)
    FLASH_SetErrorCode                       0x080009dd   Thumb Code    84  stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode)
    i.FLASH_WaitForLastOperation             0x08000a38   Section        0  stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation)
    i.FPGA_GetLockStatus                     0x08000a8c   Section        0  fpga_ctrl.o(i.FPGA_GetLockStatus)
    i.FPGA_GetTemperature                    0x08000a9c   Section        0  fpga_ctrl.o(i.FPGA_GetTemperature)
    i.FPGA_ProcessChannelSet                 0x08000aa8   Section        0  fpga_ctrl.o(i.FPGA_ProcessChannelSet)
    i.FPGA_ReadParams                        0x08000b2c   Section        0  fpga_ctrl.o(i.FPGA_ReadParams)
    i.FPGA_SendFrame                         0x08000b5c   Section        0  fpga_ctrl.o(i.FPGA_SendFrame)
    FPGA_SendFrame                           0x08000b5d   Thumb Code    62  fpga_ctrl.o(i.FPGA_SendFrame)
    i.FPGA_SetChannelParams                  0x08000ba4   Section        0  fpga_ctrl.o(i.FPGA_SetChannelParams)
    i.GetMarkAddr                            0x08000c30   Section        0  gpio.o(i.GetMarkAddr)
    i.HAL_ADCEx_Calibration_Start            0x08000c6c   Section        0  stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start)
    i.HAL_ADC_Init                           0x08000d60   Section        0  stm32f1xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08000e78   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_CAN_ActivateNotification           0x08000ed0   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_ActivateNotification)
    i.HAL_CAN_AddTxMessage                   0x08000ef4   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_AddTxMessage)
    i.HAL_CAN_ConfigFilter                   0x08000fe8   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_ConfigFilter)
    i.HAL_CAN_ErrorCallback                  0x080010f8   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    i.HAL_CAN_GetRxMessage                   0x080010fa   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_GetRxMessage)
    i.HAL_CAN_IRQHandler                     0x08001200   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler)
    i.HAL_CAN_Init                           0x080013fc   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_Init)
    i.HAL_CAN_IsTxMessagePending             0x08001558   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_IsTxMessagePending)
    i.HAL_CAN_MspInit                        0x08001578   Section        0  can.o(i.HAL_CAN_MspInit)
    i.HAL_CAN_RxFifo0FullCallback            0x08001660   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    i.HAL_CAN_RxFifo0MsgPendingCallback      0x08001664   Section        0  can.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    i.HAL_CAN_RxFifo1FullCallback            0x08001728   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    i.HAL_CAN_RxFifo1MsgPendingCallback      0x0800172a   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    i.HAL_CAN_SleepCallback                  0x0800172c   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_SleepCallback)
    i.HAL_CAN_Start                          0x0800172e   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_Start)
    i.HAL_CAN_TxMailbox0AbortCallback        0x08001786   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    i.HAL_CAN_TxMailbox0CompleteCallback     0x08001788   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    i.HAL_CAN_TxMailbox1AbortCallback        0x0800178a   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    i.HAL_CAN_TxMailbox1CompleteCallback     0x0800178c   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    i.HAL_CAN_TxMailbox2AbortCallback        0x0800178e   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    i.HAL_CAN_TxMailbox2CompleteCallback     0x08001790   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    i.HAL_CAN_WakeUpFromRxMsgCallback        0x08001792   Section        0  stm32f1xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    i.HAL_DMA_Abort                          0x08001794   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080017dc   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_Start_IT                       0x0800192c   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_FLASHEx_Erase                      0x080019a0   Section        0  stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase)
    i.HAL_FLASH_Lock                         0x08001a48   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock)
    i.HAL_FLASH_Program                      0x08001a5c   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Program)
    i.HAL_FLASH_Unlock                       0x08001ae8   Section        0  stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock)
    i.HAL_GPIO_Init                          0x08001b10   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08001d00   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001d10   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08001d1c   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001d2c   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001d50   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001d94   Section        0  stm32f1xx_hal.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08001d96   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001db0   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001df0   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCCEx_GetPeriphCLKFreq             0x08001e14   Section        0  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq)
    i.HAL_RCCEx_PeriphCLKConfig              0x08001f98   Section        0  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08002138   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x0800228c   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK2Freq                   0x08002298   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080022b8   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08002384   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_Init                           0x080027dc   Section        0  stm32f1xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x08002890   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SYSTICK_CLKSourceConfig            0x0800294c   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    i.HAL_SYSTICK_Config                     0x08002968   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x08002990   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08002992   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIM_Base_Init                      0x08002994   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x080029e0   Section        0  timer.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08002a18   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_IC_CaptureCallback             0x08002a84   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08002a86   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08002bf4   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08002bf6   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08002bf8   Section        0  timer.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08002c10   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x08002c12   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08002c14   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x08002c6c   Section        0  usart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08002ca4   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_IdleCallback                  0x08002e70   Section        0  usart.o(i.HAL_UART_IdleCallback)
    i.HAL_UART_Receive_DMA                   0x08002eb0   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA)
    i.HAL_UART_RxCpltCallback                0x08002ede   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08002ee0   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_TxCpltCallback                0x08002ee2   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08002ee4   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.InitModuleParams                       0x08002ee8   Section        0  candataprocess.o(i.InitModuleParams)
    i.LED_Init                               0x08002f9c   Section        0  timer.o(i.LED_Init)
    i.MemManage_Handler                      0x08002fd4   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08002fd6   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08002fd8   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.ProcessBitQuery                        0x08002fdc   Section        0  candataprocess.o(i.ProcessBitQuery)
    ProcessBitQuery                          0x08002fdd   Thumb Code    82  candataprocess.o(i.ProcessBitQuery)
    i.ProcessParamQuery                      0x08003038   Section        0  candataprocess.o(i.ProcessParamQuery)
    ProcessParamQuery                        0x08003039   Thumb Code   662  candataprocess.o(i.ProcessParamQuery)
    i.ProcessParamSet                        0x080032d8   Section        0  candataprocess.o(i.ProcessParamSet)
    ProcessParamSet                          0x080032d9   Thumb Code   256  candataprocess.o(i.ProcessParamSet)
    i.ProcessProductInfoQuery                0x080033e8   Section        0  candataprocess.o(i.ProcessProductInfoQuery)
    ProcessProductInfoQuery                  0x080033e9   Thumb Code   172  candataprocess.o(i.ProcessProductInfoQuery)
    i.RCC_Delay                              0x0800349c   Section        0  stm32f1xx_hal_rcc.o(i.RCC_Delay)
    RCC_Delay                                0x0800349d   Thumb Code    30  stm32f1xx_hal_rcc.o(i.RCC_Delay)
    i.SPI1_Init                              0x080034c0   Section        0  spi.o(i.SPI1_Init)
    i.STMFLASH_Write                         0x08003540   Section        0  stm32flash.o(i.STMFLASH_Write)
    i.SVC_Handler                            0x080035b8   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.SendCanResponse                        0x080035bc   Section        0  candataprocess.o(i.SendCanResponse)
    SendCanResponse                          0x080035bd   Thumb Code    28  candataprocess.o(i.SendCanResponse)
    i.SetProductInfo                         0x080035e0   Section        0  candataprocess.o(i.SetProductInfo)
    SetProductInfo                           0x080035e1   Thumb Code    58  candataprocess.o(i.SetProductInfo)
    i.Stm32Flash_Read                        0x08003620   Section        0  stm32flash.o(i.Stm32Flash_Read)
    i.SysTick_Handler                        0x0800363a   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08003640   Section        0  sys.o(i.SystemClock_Config)
    i.SystemInit                             0x080036c0   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x080036c4   Section        0  timer.o(i.TIM2_IRQHandler)
    i.TIM2_Init                              0x080036d0   Section        0  timer.o(i.TIM2_Init)
    i.TIM_Base_SetConfig                     0x080036f8   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.UART_DMAAbortOnError                   0x08003770   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08003771   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08003780   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08003781   Thumb Code    74  stm32f1xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x080037ca   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x080037cb   Thumb Code    90  stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08003824   Section        0  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08003825   Thumb Code    26  stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x0800383e   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x0800383f   Thumb Code    48  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x0800386e   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x0800386f   Thumb Code    26  stm32f1xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08003888   Section        0  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08003889   Thumb Code    18  stm32f1xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x0800389a   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x0800389b   Thumb Code   188  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_Start_Receive_DMA                 0x08003958   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Transmit_IT                       0x080039cc   Section        0  stm32f1xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x080039cd   Thumb Code    94  stm32f1xx_hal_uart.o(i.UART_Transmit_IT)
    i.USART2_IRQHandler                      0x08003a2c   Section        0  usart.o(i.USART2_IRQHandler)
    i.UpdateBitInfo                          0x08003a54   Section        0  candataprocess.o(i.UpdateBitInfo)
    i.UsageFault_Handler                     0x08003a74   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x08003a76   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08003a77   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.delay_init                             0x08003a98   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08003aac   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08003ac8   Section        0  delay.o(i.delay_us)
    i.main                                   0x08003b00   Section        0  main.o(i.main)
    .constdata                               0x08003c00   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x08003c10   Section        8  system_stm32f1xx.o(.constdata)
    .data                                    0x20000000   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000004   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x20000010   Section        4  main.o(.data)
    .data                                    0x20000014   Section        4  delay.o(.data)
    fac_us                                   0x20000014   Data           4  delay.o(.data)
    .data                                    0x20000018   Section        2  usart.o(.data)
    .data                                    0x2000001a   Section       12  can.o(.data)
    .data                                    0x20000026   Section        2  candataprocess.o(.data)
    .data                                    0x20000028   Section        5  fpga_ctrl.o(.data)
    fpgaReadBuffer                           0x20000028   Data           5  fpga_ctrl.o(.data)
    .data                                    0x2000002d   Section        1  timer.o(.data)
    .bss                                     0x20000030   Section       32  stm32f1xx_hal_flash.o(.bss)
    .bss                                     0x20000050   Section      256  main.o(.bss)
    .bss                                     0x20000150   Section      236  usart.o(.bss)
    .bss                                     0x2000023c   Section      592  can.o(.bss)
    .bss                                     0x2000048c   Section      326  candataprocess.o(.bss)
    .bss                                     0x200005d4   Section       88  spi.o(.bss)
    .bss                                     0x2000062c   Section       48  adc.o(.bss)
    .bss                                     0x2000065c   Section       72  timer.o(.bss)
    .bss                                     0x200006a4   Section       96  libspace.o(.bss)
    HEAP                                     0x20000708   Section      512  startup_stm32f107xc.o(HEAP)
    Heap_Mem                                 0x20000708   Data         512  startup_stm32f107xc.o(HEAP)
    STACK                                    0x20000908   Section     1024  startup_stm32f107xc.o(STACK)
    Stack_Mem                                0x20000908   Data        1024  startup_stm32f107xc.o(STACK)
    __initial_sp                             0x20000d08   Data           0  startup_stm32f107xc.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000150   Number         0  startup_stm32f107xc.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f107xc.o(RESET)
    __Vectors_End                            0x08000150   Data           0  startup_stm32f107xc.o(RESET)
    __main                                   0x08000151   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000159   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000159   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000159   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000167   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800018d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001a9   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001c5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001c7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001c9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001cb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080001cb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001cb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080001cb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080001cb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080001cb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080001cb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080001cb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080001cd   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001cd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001cd   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001d3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001d3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001d7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001d7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001df   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001e1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001e1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001e5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001ed   Thumb Code     8  startup_stm32f107xc.o(.text)
    ADC1_2_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    CAN1_RX1_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    CAN1_SCE_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    CAN1_TX_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    CAN2_RX1_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    CAN2_SCE_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    CAN2_TX_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA2_Channel4_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    DMA2_Channel5_IRQHandler                 0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    ETH_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    ETH_WKUP_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    EXTI0_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    EXTI15_10_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    EXTI1_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    EXTI2_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    EXTI3_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    EXTI4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    EXTI9_5_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    FLASH_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    I2C1_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    I2C1_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    I2C2_ER_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    I2C2_EV_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    OTG_FS_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    PVD_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    RCC_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    RTC_Alarm_IRQHandler                     0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    RTC_IRQHandler                           0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    SPI1_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    SPI2_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    SPI3_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    TAMPER_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    TIM1_BRK_IRQHandler                      0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    TIM1_CC_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    TIM1_UP_IRQHandler                       0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    TIM3_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    TIM4_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    TIM5_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    TIM6_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    TIM7_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    UART4_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    UART5_IRQHandler                         0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    USART1_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    USART3_IRQHandler                        0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    WWDG_IRQHandler                          0x08000207   Thumb Code     0  startup_stm32f107xc.o(.text)
    __user_initial_stackheap                 0x08000209   Thumb Code     0  startup_stm32f107xc.o(.text)
    __aeabi_uldivmod                         0x0800022d   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x0800022d   Thumb Code   238  lludivv7m.o(.text)
    __aeabi_llsr                             0x0800031b   Thumb Code     0  llushr.o(.text)
    _ll_ushift_r                             0x0800031b   Thumb Code    38  llushr.o(.text)
    __aeabi_memcpy                           0x08000341   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000341   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080003a7   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memset                           0x080003cb   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr                           0x080003db   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x080003db   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x080003df   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x0800041f   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800041f   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800041f   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000423   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x0800046d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800046f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000471   Thumb Code     2  heapauxi.o(.text)
    __aeabi_memcpy4                          0x08000473   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000473   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000473   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080004bb   Thumb Code     0  rt_memcpy_w.o(.text)
    __user_setup_stackheap                   0x080004d7   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000521   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000535   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000535   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000535   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x0800053d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000549   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000549   Thumb Code     2  use_no_semi.o(.text)
    ADC_ConversionStop_Disable               0x0800054b   Thumb Code    84  stm32f1xx_hal_adc.o(i.ADC_ConversionStop_Disable)
    __semihosting_library_function           0x0800054b   Thumb Code     0  indicate_semi.o(.text)
    ADC_Enable                               0x080005a1   Thumb Code   118  stm32f1xx_hal_adc.o(i.ADC_Enable)
    ADC_Init                                 0x08000621   Thumb Code    68  adc.o(i.ADC_Init)
    BusFault_Handler                         0x0800066d   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    CAN1_Init                                0x08000671   Thumb Code   138  can.o(i.CAN1_Init)
    CAN1_RX0_IRQHandler                      0x08000719   Thumb Code     6  can.o(i.CAN1_RX0_IRQHandler)
    CAN2_Init                                0x08000725   Thumb Code   140  can.o(i.CAN2_Init)
    CAN2_RX0_IRQHandler                      0x080007cd   Thumb Code     6  can.o(i.CAN2_RX0_IRQHandler)
    CANxSend                                 0x080007d9   Thumb Code   146  can.o(i.CANxSend)
    CanRxDataProcess                         0x08000875   Thumb Code   182  candataprocess.o(i.CanRxDataProcess)
    DebugMon_Handler                         0x0800096d   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x0800096f   Thumb Code     8  sys.o(i.Error_Handler)
    FLASH_PageErase                          0x0800099d   Thumb Code    28  stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase)
    FLASH_WaitForLastOperation               0x08000a39   Thumb Code    80  stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation)
    FPGA_GetLockStatus                       0x08000a8d   Thumb Code    12  fpga_ctrl.o(i.FPGA_GetLockStatus)
    FPGA_GetTemperature                      0x08000a9d   Thumb Code     8  fpga_ctrl.o(i.FPGA_GetTemperature)
    FPGA_ProcessChannelSet                   0x08000aa9   Thumb Code   130  fpga_ctrl.o(i.FPGA_ProcessChannelSet)
    FPGA_ReadParams                          0x08000b2d   Thumb Code    42  fpga_ctrl.o(i.FPGA_ReadParams)
    FPGA_SetChannelParams                    0x08000ba5   Thumb Code   138  fpga_ctrl.o(i.FPGA_SetChannelParams)
    GetMarkAddr                              0x08000c31   Thumb Code    48  gpio.o(i.GetMarkAddr)
    HAL_ADCEx_Calibration_Start              0x08000c6d   Thumb Code   238  stm32f1xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start)
    HAL_ADC_Init                             0x08000d61   Thumb Code   272  stm32f1xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08000e79   Thumb Code    76  adc.o(i.HAL_ADC_MspInit)
    HAL_CAN_ActivateNotification             0x08000ed1   Thumb Code    36  stm32f1xx_hal_can.o(i.HAL_CAN_ActivateNotification)
    HAL_CAN_AddTxMessage                     0x08000ef5   Thumb Code   242  stm32f1xx_hal_can.o(i.HAL_CAN_AddTxMessage)
    HAL_CAN_ConfigFilter                     0x08000fe9   Thumb Code   266  stm32f1xx_hal_can.o(i.HAL_CAN_ConfigFilter)
    HAL_CAN_ErrorCallback                    0x080010f9   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_ErrorCallback)
    HAL_CAN_GetRxMessage                     0x080010fb   Thumb Code   262  stm32f1xx_hal_can.o(i.HAL_CAN_GetRxMessage)
    HAL_CAN_IRQHandler                       0x08001201   Thumb Code   508  stm32f1xx_hal_can.o(i.HAL_CAN_IRQHandler)
    HAL_CAN_Init                             0x080013fd   Thumb Code   348  stm32f1xx_hal_can.o(i.HAL_CAN_Init)
    HAL_CAN_IsTxMessagePending               0x08001559   Thumb Code    30  stm32f1xx_hal_can.o(i.HAL_CAN_IsTxMessagePending)
    HAL_CAN_MspInit                          0x08001579   Thumb Code   210  can.o(i.HAL_CAN_MspInit)
    HAL_CAN_RxFifo0FullCallback              0x08001661   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback)
    HAL_CAN_RxFifo0MsgPendingCallback        0x08001665   Thumb Code   158  can.o(i.HAL_CAN_RxFifo0MsgPendingCallback)
    HAL_CAN_RxFifo1FullCallback              0x08001729   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback)
    HAL_CAN_RxFifo1MsgPendingCallback        0x0800172b   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback)
    HAL_CAN_SleepCallback                    0x0800172d   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_SleepCallback)
    HAL_CAN_Start                            0x0800172f   Thumb Code    88  stm32f1xx_hal_can.o(i.HAL_CAN_Start)
    HAL_CAN_TxMailbox0AbortCallback          0x08001787   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback)
    HAL_CAN_TxMailbox0CompleteCallback       0x08001789   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback)
    HAL_CAN_TxMailbox1AbortCallback          0x0800178b   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback)
    HAL_CAN_TxMailbox1CompleteCallback       0x0800178d   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback)
    HAL_CAN_TxMailbox2AbortCallback          0x0800178f   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback)
    HAL_CAN_TxMailbox2CompleteCallback       0x08001791   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback)
    HAL_CAN_WakeUpFromRxMsgCallback          0x08001793   Thumb Code     2  stm32f1xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback)
    HAL_DMA_Abort                            0x08001795   Thumb Code    70  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080017dd   Thumb Code   328  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_Start_IT                         0x0800192d   Thumb Code   116  stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_FLASHEx_Erase                        0x080019a1   Thumb Code   158  stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase)
    HAL_FLASH_Lock                           0x08001a49   Thumb Code    14  stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock)
    HAL_FLASH_Program                        0x08001a5d   Thumb Code   132  stm32f1xx_hal_flash.o(i.HAL_FLASH_Program)
    HAL_FLASH_Unlock                         0x08001ae9   Thumb Code    28  stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock)
    HAL_GPIO_Init                            0x08001b11   Thumb Code   464  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08001d01   Thumb Code    14  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001d11   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08001d1d   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001d2d   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001d51   Thumb Code    58  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001d95   Thumb Code     2  stm32f1xx_hal.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001d97   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001db1   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001df1   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCCEx_GetPeriphCLKFreq               0x08001e15   Thumb Code   344  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq)
    HAL_RCCEx_PeriphCLKConfig                0x08001f99   Thumb Code   400  stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08002139   Thumb Code   318  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x0800228d   Thumb Code     6  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK2Freq                     0x08002299   Thumb Code    22  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080022b9   Thumb Code   160  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08002385   Thumb Code  1112  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x080027dd   Thumb Code   180  stm32f1xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x08002891   Thumb Code   172  spi.o(i.HAL_SPI_MspInit)
    HAL_SYSTICK_CLKSourceConfig              0x0800294d   Thumb Code    28  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    HAL_SYSTICK_Config                       0x08002969   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x08002991   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08002993   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIM_Base_Init                        0x08002995   Thumb Code    74  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080029e1   Thumb Code    50  timer.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08002a19   Thumb Code    92  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_IC_CaptureCallback               0x08002a85   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08002a87   Thumb Code   366  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x08002bf5   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x08002bf7   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08002bf9   Thumb Code    14  timer.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08002c11   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x08002c13   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08002c15   Thumb Code    88  stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08002c6d   Thumb Code    48  usart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002ca5   Thumb Code   456  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_IdleCallback                    0x08002e71   Thumb Code    50  usart.o(i.HAL_UART_IdleCallback)
    HAL_UART_Receive_DMA                     0x08002eb1   Thumb Code    46  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA)
    HAL_UART_RxCpltCallback                  0x08002edf   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08002ee1   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_TxCpltCallback                  0x08002ee3   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08002ee5   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    InitModuleParams                         0x08002ee9   Thumb Code   160  candataprocess.o(i.InitModuleParams)
    LED_Init                                 0x08002f9d   Thumb Code    48  timer.o(i.LED_Init)
    MemManage_Handler                        0x08002fd5   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08002fd7   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08002fd9   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    SPI1_Init                                0x080034c1   Thumb Code   100  spi.o(i.SPI1_Init)
    STMFLASH_Write                           0x08003541   Thumb Code    96  stm32flash.o(i.STMFLASH_Write)
    SVC_Handler                              0x080035b9   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    Stm32Flash_Read                          0x08003621   Thumb Code    26  stm32flash.o(i.Stm32Flash_Read)
    SysTick_Handler                          0x0800363b   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08003641   Thumb Code   106  sys.o(i.SystemClock_Config)
    SystemInit                               0x080036c1   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x080036c5   Thumb Code     6  timer.o(i.TIM2_IRQHandler)
    TIM2_Init                                0x080036d1   Thumb Code    36  timer.o(i.TIM2_Init)
    TIM_Base_SetConfig                       0x080036f9   Thumb Code   104  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    UART_Start_Receive_DMA                   0x08003959   Thumb Code   102  stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART2_IRQHandler                        0x08003a2d   Thumb Code    34  usart.o(i.USART2_IRQHandler)
    UpdateBitInfo                            0x08003a55   Thumb Code    28  candataprocess.o(i.UpdateBitInfo)
    UsageFault_Handler                       0x08003a75   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    delay_init                               0x08003a99   Thumb Code    16  delay.o(i.delay_init)
    delay_ms                                 0x08003aad   Thumb Code    26  delay.o(i.delay_ms)
    delay_us                                 0x08003ac9   Thumb Code    50  delay.o(i.delay_us)
    main                                     0x08003b01   Thumb Code   222  main.o(i.main)
    AHBPrescTable                            0x08003c00   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x08003c10   Data           8  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x08003c18   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003c38   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f1xx.o(.data)
    uwTickFreq                               0x20000004   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x2000000c   Data           4  stm32f1xx_hal.o(.data)
    i                                        0x20000010   Data           1  main.o(.data)
    MarkAddr                                 0x20000011   Data           1  main.o(.data)
    CanDataLength                            0x20000012   Data           2  main.o(.data)
    USART_RX_STA                             0x20000018   Data           2  usart.o(.data)
    Can1RxCpt                                0x2000001a   Data           2  can.o(.data)
    Can2RxCpt                                0x2000001c   Data           2  can.o(.data)
    CanIdConfig                              0x2000001e   Data           8  can.o(.data)
    canTxLength                              0x20000026   Data           2  candataprocess.o(.data)
    Time_1S                                  0x2000002d   Data           1  timer.o(.data)
    pFlash                                   0x20000030   Data          32  stm32f1xx_hal_flash.o(.bss)
    CanRxData                                0x20000050   Data         256  main.o(.bss)
    USART_RX_BUF                             0x20000150   Data         100  usart.o(.bss)
    huart2                                   0x200001b4   Data          68  usart.o(.bss)
    hdma_usart2_rx                           0x200001f8   Data          68  usart.o(.bss)
    hcan1                                    0x2000023c   Data          40  can.o(.bss)
    hcan2                                    0x20000264   Data          40  can.o(.bss)
    Can1RxData                               0x2000028c   Data         256  can.o(.bss)
    Can2RxData                               0x2000038c   Data         256  can.o(.bss)
    moduleParams                             0x2000048c   Data          44  candataprocess.o(.bss)
    productInfo                              0x200004b8   Data          12  candataprocess.o(.bss)
    bitInfo                                  0x200004c4   Data          14  candataprocess.o(.bss)
    canTxBuffer                              0x200004d2   Data         256  candataprocess.o(.bss)
    hspi1                                    0x200005d4   Data          88  spi.o(.bss)
    hadc1                                    0x2000062c   Data          48  adc.o(.bss)
    htim2                                    0x2000065c   Data          72  timer.o(.bss)
    __libspace_start                         0x200006a4   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000704   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000151

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003c68, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003c38, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000150   Data   RO            3    RESET               startup_stm32f107xc.o
    0x08000150   0x08000150   0x00000008   Code   RO         5206  * !!!main             c_w.l(__main.o)
    0x08000158   0x08000158   0x00000034   Code   RO         5440    !!!scatter          c_w.l(__scatter.o)
    0x0800018c   0x0800018c   0x0000001a   Code   RO         5442    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001a6   0x080001a6   0x00000002   PAD
    0x080001a8   0x080001a8   0x0000001c   Code   RO         5444    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001c4   0x080001c4   0x00000002   Code   RO         5306    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5321    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5323    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5326    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5328    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5330    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5333    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5335    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5337    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5339    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5341    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5343    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5345    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5347    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5349    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5351    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5353    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5357    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5359    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5361    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         5363    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001c6   0x080001c6   0x00000002   Code   RO         5364    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001c8   0x080001c8   0x00000002   Code   RO         5395    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001ca   0x080001ca   0x00000000   Code   RO         5421    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001ca   0x080001ca   0x00000000   Code   RO         5423    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001ca   0x080001ca   0x00000000   Code   RO         5425    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001ca   0x080001ca   0x00000000   Code   RO         5428    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001ca   0x080001ca   0x00000000   Code   RO         5431    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001ca   0x080001ca   0x00000000   Code   RO         5433    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001ca   0x080001ca   0x00000000   Code   RO         5436    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080001ca   0x080001ca   0x00000002   Code   RO         5437    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080001cc   0x080001cc   0x00000000   Code   RO         5256    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001cc   0x080001cc   0x00000000   Code   RO         5275    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001cc   0x080001cc   0x00000006   Code   RO         5287    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO         5277    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001d2   0x080001d2   0x00000004   Code   RO         5278    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001d6   0x080001d6   0x00000000   Code   RO         5280    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001d6   0x080001d6   0x00000008   Code   RO         5281    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001de   0x080001de   0x00000002   Code   RO         5312    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001e0   0x080001e0   0x00000000   Code   RO         5368    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001e0   0x080001e0   0x00000004   Code   RO         5369    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001e4   0x080001e4   0x00000006   Code   RO         5370    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001ea   0x080001ea   0x00000002   PAD
    0x080001ec   0x080001ec   0x00000040   Code   RO            4    .text               startup_stm32f107xc.o
    0x0800022c   0x0800022c   0x000000ee   Code   RO         5192    .text               c_w.l(lludivv7m.o)
    0x0800031a   0x0800031a   0x00000026   Code   RO         5194    .text               c_w.l(llushr.o)
    0x08000340   0x08000340   0x0000008a   Code   RO         5196    .text               c_w.l(rt_memcpy_v6.o)
    0x080003ca   0x080003ca   0x00000010   Code   RO         5198    .text               c_w.l(aeabi_memset.o)
    0x080003da   0x080003da   0x00000044   Code   RO         5200    .text               c_w.l(rt_memclr.o)
    0x0800041e   0x0800041e   0x0000004e   Code   RO         5202    .text               c_w.l(rt_memclr_w.o)
    0x0800046c   0x0800046c   0x00000006   Code   RO         5204    .text               c_w.l(heapauxi.o)
    0x08000472   0x08000472   0x00000064   Code   RO         5261    .text               c_w.l(rt_memcpy_w.o)
    0x080004d6   0x080004d6   0x0000004a   Code   RO         5293    .text               c_w.l(sys_stackheap_outer.o)
    0x08000520   0x08000520   0x00000012   Code   RO         5295    .text               c_w.l(exit.o)
    0x08000532   0x08000532   0x00000002   PAD
    0x08000534   0x08000534   0x00000008   Code   RO         5309    .text               c_w.l(libspace.o)
    0x0800053c   0x0800053c   0x0000000c   Code   RO         5365    .text               c_w.l(sys_exit.o)
    0x08000548   0x08000548   0x00000002   Code   RO         5384    .text               c_w.l(use_no_semi.o)
    0x0800054a   0x0800054a   0x00000000   Code   RO         5386    .text               c_w.l(indicate_semi.o)
    0x0800054a   0x0800054a   0x00000054   Code   RO          328    i.ADC_ConversionStop_Disable  stm32f1xx_hal_adc.o
    0x0800059e   0x0800059e   0x00000002   PAD
    0x080005a0   0x080005a0   0x00000080   Code   RO          332    i.ADC_Enable        stm32f1xx_hal_adc.o
    0x08000620   0x08000620   0x0000004c   Code   RO         4693    i.ADC_Init          adc.o
    0x0800066c   0x0800066c   0x00000002   Code   RO         4265    i.BusFault_Handler  stm32f1xx_it.o
    0x0800066e   0x0800066e   0x00000002   PAD
    0x08000670   0x08000670   0x000000a8   Code   RO         4476    i.CAN1_Init         can.o
    0x08000718   0x08000718   0x0000000c   Code   RO         4477    i.CAN1_RX0_IRQHandler  can.o
    0x08000724   0x08000724   0x000000a8   Code   RO         4478    i.CAN2_Init         can.o
    0x080007cc   0x080007cc   0x0000000c   Code   RO         4479    i.CAN2_RX0_IRQHandler  can.o
    0x080007d8   0x080007d8   0x0000009c   Code   RO         4480    i.CANxSend          can.o
    0x08000874   0x08000874   0x000000c0   Code   RO         4543    i.CanRxDataProcess  candataprocess.o
    0x08000934   0x08000934   0x0000000c   Code   RO         4780    i.ConvertLowFrequency  fpga_ctrl.o
    0x08000940   0x08000940   0x0000002c   Code   RO         1009    i.DMA_SetConfig     stm32f1xx_hal_dma.o
    0x0800096c   0x0800096c   0x00000002   Code   RO         4266    i.DebugMon_Handler  stm32f1xx_it.o
    0x0800096e   0x0800096e   0x00000008   Code   RO         4379    i.Error_Handler     sys.o
    0x08000976   0x08000976   0x00000002   PAD
    0x08000978   0x08000978   0x00000024   Code   RO         1279    i.FLASH_MassErase   stm32f1xx_hal_flash_ex.o
    0x0800099c   0x0800099c   0x00000024   Code   RO         1288    i.FLASH_PageErase   stm32f1xx_hal_flash_ex.o
    0x080009c0   0x080009c0   0x0000001c   Code   RO         1176    i.FLASH_Program_HalfWord  stm32f1xx_hal_flash.o
    0x080009dc   0x080009dc   0x0000005c   Code   RO         1177    i.FLASH_SetErrorCode  stm32f1xx_hal_flash.o
    0x08000a38   0x08000a38   0x00000054   Code   RO         1178    i.FLASH_WaitForLastOperation  stm32f1xx_hal_flash.o
    0x08000a8c   0x08000a8c   0x00000010   Code   RO         4781    i.FPGA_GetLockStatus  fpga_ctrl.o
    0x08000a9c   0x08000a9c   0x0000000c   Code   RO         4782    i.FPGA_GetTemperature  fpga_ctrl.o
    0x08000aa8   0x08000aa8   0x00000082   Code   RO         4783    i.FPGA_ProcessChannelSet  fpga_ctrl.o
    0x08000b2a   0x08000b2a   0x00000002   PAD
    0x08000b2c   0x08000b2c   0x00000030   Code   RO         4784    i.FPGA_ReadParams   fpga_ctrl.o
    0x08000b5c   0x08000b5c   0x00000048   Code   RO         4785    i.FPGA_SendFrame    fpga_ctrl.o
    0x08000ba4   0x08000ba4   0x0000008a   Code   RO         4786    i.FPGA_SetChannelParams  fpga_ctrl.o
    0x08000c2e   0x08000c2e   0x00000002   PAD
    0x08000c30   0x08000c30   0x0000003c   Code   RO         4750    i.GetMarkAddr       gpio.o
    0x08000c6c   0x08000c6c   0x000000f4   Code   RO          508    i.HAL_ADCEx_Calibration_Start  stm32f1xx_hal_adc_ex.o
    0x08000d60   0x08000d60   0x00000118   Code   RO          343    i.HAL_ADC_Init      stm32f1xx_hal_adc.o
    0x08000e78   0x08000e78   0x00000058   Code   RO         4698    i.HAL_ADC_MspInit   adc.o
    0x08000ed0   0x08000ed0   0x00000024   Code   RO          605    i.HAL_CAN_ActivateNotification  stm32f1xx_hal_can.o
    0x08000ef4   0x08000ef4   0x000000f2   Code   RO          606    i.HAL_CAN_AddTxMessage  stm32f1xx_hal_can.o
    0x08000fe6   0x08000fe6   0x00000002   PAD
    0x08000fe8   0x08000fe8   0x00000110   Code   RO          607    i.HAL_CAN_ConfigFilter  stm32f1xx_hal_can.o
    0x080010f8   0x080010f8   0x00000002   Code   RO          610    i.HAL_CAN_ErrorCallback  stm32f1xx_hal_can.o
    0x080010fa   0x080010fa   0x00000106   Code   RO          613    i.HAL_CAN_GetRxMessage  stm32f1xx_hal_can.o
    0x08001200   0x08001200   0x000001fc   Code   RO          617    i.HAL_CAN_IRQHandler  stm32f1xx_hal_can.o
    0x080013fc   0x080013fc   0x0000015c   Code   RO          618    i.HAL_CAN_Init      stm32f1xx_hal_can.o
    0x08001558   0x08001558   0x0000001e   Code   RO          620    i.HAL_CAN_IsTxMessagePending  stm32f1xx_hal_can.o
    0x08001576   0x08001576   0x00000002   PAD
    0x08001578   0x08001578   0x000000e8   Code   RO         4481    i.HAL_CAN_MspInit   can.o
    0x08001660   0x08001660   0x00000002   Code   RO          625    i.HAL_CAN_RxFifo0FullCallback  stm32f1xx_hal_can.o
    0x08001662   0x08001662   0x00000002   PAD
    0x08001664   0x08001664   0x000000c4   Code   RO         4482    i.HAL_CAN_RxFifo0MsgPendingCallback  can.o
    0x08001728   0x08001728   0x00000002   Code   RO          627    i.HAL_CAN_RxFifo1FullCallback  stm32f1xx_hal_can.o
    0x0800172a   0x0800172a   0x00000002   Code   RO          628    i.HAL_CAN_RxFifo1MsgPendingCallback  stm32f1xx_hal_can.o
    0x0800172c   0x0800172c   0x00000002   Code   RO          629    i.HAL_CAN_SleepCallback  stm32f1xx_hal_can.o
    0x0800172e   0x0800172e   0x00000058   Code   RO          630    i.HAL_CAN_Start     stm32f1xx_hal_can.o
    0x08001786   0x08001786   0x00000002   Code   RO          632    i.HAL_CAN_TxMailbox0AbortCallback  stm32f1xx_hal_can.o
    0x08001788   0x08001788   0x00000002   Code   RO          633    i.HAL_CAN_TxMailbox0CompleteCallback  stm32f1xx_hal_can.o
    0x0800178a   0x0800178a   0x00000002   Code   RO          634    i.HAL_CAN_TxMailbox1AbortCallback  stm32f1xx_hal_can.o
    0x0800178c   0x0800178c   0x00000002   Code   RO          635    i.HAL_CAN_TxMailbox1CompleteCallback  stm32f1xx_hal_can.o
    0x0800178e   0x0800178e   0x00000002   Code   RO          636    i.HAL_CAN_TxMailbox2AbortCallback  stm32f1xx_hal_can.o
    0x08001790   0x08001790   0x00000002   Code   RO          637    i.HAL_CAN_TxMailbox2CompleteCallback  stm32f1xx_hal_can.o
    0x08001792   0x08001792   0x00000002   Code   RO          639    i.HAL_CAN_WakeUpFromRxMsgCallback  stm32f1xx_hal_can.o
    0x08001794   0x08001794   0x00000046   Code   RO         1010    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x080017da   0x080017da   0x00000002   PAD
    0x080017dc   0x080017dc   0x00000150   Code   RO         1011    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x0800192c   0x0800192c   0x00000074   Code   RO         1020    i.HAL_DMA_Start_IT  stm32f1xx_hal_dma.o
    0x080019a0   0x080019a0   0x000000a8   Code   RO         1289    i.HAL_FLASHEx_Erase  stm32f1xx_hal_flash_ex.o
    0x08001a48   0x08001a48   0x00000014   Code   RO         1182    i.HAL_FLASH_Lock    stm32f1xx_hal_flash.o
    0x08001a5c   0x08001a5c   0x0000008c   Code   RO         1187    i.HAL_FLASH_Program  stm32f1xx_hal_flash.o
    0x08001ae8   0x08001ae8   0x00000028   Code   RO         1189    i.HAL_FLASH_Unlock  stm32f1xx_hal_flash.o
    0x08001b10   0x08001b10   0x000001f0   Code   RO         1388    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x08001d00   0x08001d00   0x0000000e   Code   RO         1392    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x08001d0e   0x08001d0e   0x00000002   PAD
    0x08001d10   0x08001d10   0x0000000c   Code   RO          161    i.HAL_GetTick       stm32f1xx_hal.o
    0x08001d1c   0x08001d1c   0x00000010   Code   RO          167    i.HAL_IncTick       stm32f1xx_hal.o
    0x08001d2c   0x08001d2c   0x00000024   Code   RO          168    i.HAL_Init          stm32f1xx_hal.o
    0x08001d50   0x08001d50   0x00000044   Code   RO          169    i.HAL_InitTick      stm32f1xx_hal.o
    0x08001d94   0x08001d94   0x00000002   Code   RO          171    i.HAL_MspInit       stm32f1xx_hal.o
    0x08001d96   0x08001d96   0x0000001a   Code   RO          840    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08001db0   0x08001db0   0x00000040   Code   RO          846    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08001df0   0x08001df0   0x00000024   Code   RO          847    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08001e14   0x08001e14   0x00000184   Code   RO         1724    i.HAL_RCCEx_GetPeriphCLKFreq  stm32f1xx_hal_rcc_ex.o
    0x08001f98   0x08001f98   0x000001a0   Code   RO         1725    i.HAL_RCCEx_PeriphCLKConfig  stm32f1xx_hal_rcc_ex.o
    0x08002138   0x08002138   0x00000154   Code   RO         1613    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x0800228c   0x0800228c   0x0000000c   Code   RO         1618    i.HAL_RCC_GetHCLKFreq  stm32f1xx_hal_rcc.o
    0x08002298   0x08002298   0x00000020   Code   RO         1621    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x080022b8   0x080022b8   0x000000cc   Code   RO         1622    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08002384   0x08002384   0x00000458   Code   RO         1625    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x080027dc   0x080027dc   0x000000b4   Code   RO         1790    i.HAL_SPI_Init      stm32f1xx_hal_spi.o
    0x08002890   0x08002890   0x000000bc   Code   RO         4624    i.HAL_SPI_MspInit   spi.o
    0x0800294c   0x0800294c   0x0000001c   Code   RO          849    i.HAL_SYSTICK_CLKSourceConfig  stm32f1xx_hal_cortex.o
    0x08002968   0x08002968   0x00000028   Code   RO          851    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08002990   0x08002990   0x00000002   Code   RO         2818    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x08002992   0x08002992   0x00000002   Code   RO         2819    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x08002994   0x08002994   0x0000004a   Code   RO         2103    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x080029de   0x080029de   0x00000002   PAD
    0x080029e0   0x080029e0   0x00000038   Code   RO         4841    i.HAL_TIM_Base_MspInit  timer.o
    0x08002a18   0x08002a18   0x0000006c   Code   RO         2108    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x08002a84   0x08002a84   0x00000002   Code   RO         2137    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x08002a86   0x08002a86   0x0000016e   Code   RO         2151    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x08002bf4   0x08002bf4   0x00000002   Code   RO         2154    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x08002bf6   0x08002bf6   0x00000002   Code   RO         2181    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x08002bf8   0x08002bf8   0x00000018   Code   RO         4842    i.HAL_TIM_PeriodElapsedCallback  timer.o
    0x08002c10   0x08002c10   0x00000002   Code   RO         2194    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x08002c12   0x08002c12   0x00000002   Code   RO         3096    i.HAL_UARTEx_RxEventCallback  stm32f1xx_hal_uart.o
    0x08002c14   0x08002c14   0x00000058   Code   RO         3108    i.HAL_UART_DMAStop  stm32f1xx_hal_uart.o
    0x08002c6c   0x08002c6c   0x00000038   Code   RO         4409    i.HAL_UART_ErrorCallback  usart.o
    0x08002ca4   0x08002ca4   0x000001cc   Code   RO         3113    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x08002e70   0x08002e70   0x00000040   Code   RO         4410    i.HAL_UART_IdleCallback  usart.o
    0x08002eb0   0x08002eb0   0x0000002e   Code   RO         3118    i.HAL_UART_Receive_DMA  stm32f1xx_hal_uart.o
    0x08002ede   0x08002ede   0x00000002   Code   RO         3120    i.HAL_UART_RxCpltCallback  stm32f1xx_hal_uart.o
    0x08002ee0   0x08002ee0   0x00000002   Code   RO         3121    i.HAL_UART_RxHalfCpltCallback  stm32f1xx_hal_uart.o
    0x08002ee2   0x08002ee2   0x00000002   Code   RO         3125    i.HAL_UART_TxCpltCallback  stm32f1xx_hal_uart.o
    0x08002ee4   0x08002ee4   0x00000002   Code   RO         4267    i.HardFault_Handler  stm32f1xx_it.o
    0x08002ee6   0x08002ee6   0x00000002   PAD
    0x08002ee8   0x08002ee8   0x000000b4   Code   RO         4544    i.InitModuleParams  candataprocess.o
    0x08002f9c   0x08002f9c   0x00000038   Code   RO         4843    i.LED_Init          timer.o
    0x08002fd4   0x08002fd4   0x00000002   Code   RO         4268    i.MemManage_Handler  stm32f1xx_it.o
    0x08002fd6   0x08002fd6   0x00000002   Code   RO         4269    i.NMI_Handler       stm32f1xx_it.o
    0x08002fd8   0x08002fd8   0x00000002   Code   RO         4270    i.PendSV_Handler    stm32f1xx_it.o
    0x08002fda   0x08002fda   0x00000002   PAD
    0x08002fdc   0x08002fdc   0x0000005c   Code   RO         4545    i.ProcessBitQuery   candataprocess.o
    0x08003038   0x08003038   0x000002a0   Code   RO         4546    i.ProcessParamQuery  candataprocess.o
    0x080032d8   0x080032d8   0x00000110   Code   RO         4547    i.ProcessParamSet   candataprocess.o
    0x080033e8   0x080033e8   0x000000b4   Code   RO         4548    i.ProcessProductInfoQuery  candataprocess.o
    0x0800349c   0x0800349c   0x00000024   Code   RO         1626    i.RCC_Delay         stm32f1xx_hal_rcc.o
    0x080034c0   0x080034c0   0x00000080   Code   RO         4625    i.SPI1_Init         spi.o
    0x08003540   0x08003540   0x00000078   Code   RO         4663    i.STMFLASH_Write    stm32flash.o
    0x080035b8   0x080035b8   0x00000002   Code   RO         4271    i.SVC_Handler       stm32f1xx_it.o
    0x080035ba   0x080035ba   0x00000002   PAD
    0x080035bc   0x080035bc   0x00000024   Code   RO         4549    i.SendCanResponse   candataprocess.o
    0x080035e0   0x080035e0   0x00000040   Code   RO         4550    i.SetProductInfo    candataprocess.o
    0x08003620   0x08003620   0x0000001a   Code   RO         4664    i.Stm32Flash_Read   stm32flash.o
    0x0800363a   0x0800363a   0x00000004   Code   RO         4272    i.SysTick_Handler   stm32f1xx_it.o
    0x0800363e   0x0800363e   0x00000002   PAD
    0x08003640   0x08003640   0x00000080   Code   RO         4380    i.SystemClock_Config  sys.o
    0x080036c0   0x080036c0   0x00000002   Code   RO           14    i.SystemInit        system_stm32f1xx.o
    0x080036c2   0x080036c2   0x00000002   PAD
    0x080036c4   0x080036c4   0x0000000c   Code   RO         4844    i.TIM2_IRQHandler   timer.o
    0x080036d0   0x080036d0   0x00000028   Code   RO         4845    i.TIM2_Init         timer.o
    0x080036f8   0x080036f8   0x00000078   Code   RO         2196    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x08003770   0x08003770   0x00000010   Code   RO         3127    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x08003780   0x08003780   0x0000004a   Code   RO         3128    i.UART_DMAError     stm32f1xx_hal_uart.o
    0x080037ca   0x080037ca   0x0000005a   Code   RO         3129    i.UART_DMAReceiveCplt  stm32f1xx_hal_uart.o
    0x08003824   0x08003824   0x0000001a   Code   RO         3131    i.UART_DMARxHalfCplt  stm32f1xx_hal_uart.o
    0x0800383e   0x0800383e   0x00000030   Code   RO         3137    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x0800386e   0x0800386e   0x0000001a   Code   RO         3138    i.UART_EndTransmit_IT  stm32f1xx_hal_uart.o
    0x08003888   0x08003888   0x00000012   Code   RO         3139    i.UART_EndTxTransfer  stm32f1xx_hal_uart.o
    0x0800389a   0x0800389a   0x000000bc   Code   RO         3140    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x08003956   0x08003956   0x00000002   PAD
    0x08003958   0x08003958   0x00000074   Code   RO         3142    i.UART_Start_Receive_DMA  stm32f1xx_hal_uart.o
    0x080039cc   0x080039cc   0x0000005e   Code   RO         3144    i.UART_Transmit_IT  stm32f1xx_hal_uart.o
    0x08003a2a   0x08003a2a   0x00000002   PAD
    0x08003a2c   0x08003a2c   0x00000028   Code   RO         4412    i.USART2_IRQHandler  usart.o
    0x08003a54   0x08003a54   0x00000020   Code   RO         4551    i.UpdateBitInfo     candataprocess.o
    0x08003a74   0x08003a74   0x00000002   Code   RO         4273    i.UsageFault_Handler  stm32f1xx_it.o
    0x08003a76   0x08003a76   0x00000020   Code   RO          853    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08003a96   0x08003a96   0x00000002   PAD
    0x08003a98   0x08003a98   0x00000014   Code   RO         4341    i.delay_init        delay.o
    0x08003aac   0x08003aac   0x0000001a   Code   RO         4342    i.delay_ms          delay.o
    0x08003ac6   0x08003ac6   0x00000002   PAD
    0x08003ac8   0x08003ac8   0x00000038   Code   RO         4343    i.delay_us          delay.o
    0x08003b00   0x08003b00   0x00000100   Code   RO         4190    i.main              main.o
    0x08003c00   0x08003c00   0x00000010   Data   RO           15    .constdata          system_stm32f1xx.o
    0x08003c10   0x08003c10   0x00000008   Data   RO           16    .constdata          system_stm32f1xx.o
    0x08003c18   0x08003c18   0x00000020   Data   RO         5438    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003c38, Size: 0x00000d08, Max: 0x00018000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003c38   0x00000004   Data   RW           17    .data               system_stm32f1xx.o
    0x20000004   0x08003c3c   0x0000000c   Data   RW          175    .data               stm32f1xx_hal.o
    0x20000010   0x08003c48   0x00000004   Data   RW         4192    .data               main.o
    0x20000014   0x08003c4c   0x00000004   Data   RW         4344    .data               delay.o
    0x20000018   0x08003c50   0x00000002   Data   RW         4417    .data               usart.o
    0x2000001a   0x08003c52   0x0000000c   Data   RW         4484    .data               can.o
    0x20000026   0x08003c5e   0x00000002   Data   RW         4553    .data               candataprocess.o
    0x20000028   0x08003c60   0x00000005   Data   RW         4787    .data               fpga_ctrl.o
    0x2000002d   0x08003c65   0x00000001   Data   RW         4847    .data               timer.o
    0x2000002e   0x08003c66   0x00000002   PAD
    0x20000030        -       0x00000020   Zero   RW         1190    .bss                stm32f1xx_hal_flash.o
    0x20000050        -       0x00000100   Zero   RW         4191    .bss                main.o
    0x20000150        -       0x000000ec   Zero   RW         4416    .bss                usart.o
    0x2000023c        -       0x00000250   Zero   RW         4483    .bss                can.o
    0x2000048c        -       0x00000146   Zero   RW         4552    .bss                candataprocess.o
    0x200005d2   0x08003c66   0x00000002   PAD
    0x200005d4        -       0x00000058   Zero   RW         4627    .bss                spi.o
    0x2000062c        -       0x00000030   Zero   RW         4699    .bss                adc.o
    0x2000065c        -       0x00000048   Zero   RW         4846    .bss                timer.o
    0x200006a4        -       0x00000060   Zero   RW         5310    .bss                c_w.l(libspace.o)
    0x20000704   0x08003c66   0x00000004   PAD
    0x20000708        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f107xc.o
    0x20000908        -       0x00000400   Zero   RW            1    STACK               startup_stm32f107xc.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       164         20          0          0         48       1737   adc.o
       944        140          0         12        592       5609   can.o
      1720        112          0          2        326       8869   candataprocess.o
       102         10          0          4          0       1847   delay.o
       428         40          0          5          0       5178   fpga_ctrl.o
        60         12          0          0          0        466   gpio.o
       256         34          0          4        256       2208   main.o
       316         44          0          0         88       1794   spi.o
        64         26        336          0       1536        948   startup_stm32f107xc.o
       134         24          0         12          0       7035   stm32f1xx_hal.o
       492         18          0          0          0       3185   stm32f1xx_hal_adc.o
       244          6          0          0          0       1542   stm32f1xx_hal_adc_ex.o
      1810          6          0          0          0      15276   stm32f1xx_hal_can.o
       226         14          0          0          0      29817   stm32f1xx_hal_cortex.o
       566          8          0          0          0       3518   stm32f1xx_hal_dma.o
       404         46          0          0         32       4482   stm32f1xx_hal_flash.o
       240         28          0          0          0       2687   stm32f1xx_hal_flash_ex.o
       510         32          0          0          0       2876   stm32f1xx_hal_gpio.o
      1736        104          0          0          0       6709   stm32f1xx_hal_rcc.o
       804         60          0          0          0       3389   stm32f1xx_hal_rcc_ex.o
       180          0          0          0          0       1191   stm32f1xx_hal_spi.o
       676         32          0          0          0       5907   stm32f1xx_hal_tim.o
         4          0          0          0          0       1581   stm32f1xx_hal_tim_ex.o
      1298         18          0          0          0      12404   stm32f1xx_hal_uart.o
        20          0          0          0          0       3978   stm32f1xx_it.o
       146         24          0          0          0       1811   stm32flash.o
       136         22          0          0          0       1145   sys.o
         2          0         24          4          0     682199   system_stm32f1xx.o
       188         34          0          1         72       3224   timer.o
       160         28          0          2        236       2379   usart.o

    ----------------------------------------------------------------------
     14070        <USER>        <GROUP>         48       3188     824991   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        40          0          0          2          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        16          0          0          0          0         68   aeabi_memset.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       238          0          0          0          0        100   lludivv7m.o
        38          0          0          0          0         68   llushr.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       954         <USER>          <GROUP>          0        100       1116   Library Totals
         6          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       948         16          0          0         96       1116   c_w.l

    ----------------------------------------------------------------------
       954         <USER>          <GROUP>          0        100       1116   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     15024        958        392         48       3288     818563   Grand Totals
     15024        958        392         48       3288     818563   ELF Image Totals
     15024        958        392         48          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                15416 (  15.05kB)
    Total RW  Size (RW Data + ZI Data)              3336 (   3.26kB)
    Total ROM Size (Code + RO Data + RW Data)      15464 (  15.10kB)

==============================================================================

