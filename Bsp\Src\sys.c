/**
  ******************************************************************************
  * @file       : sys.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : 时钟初始化
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */
  
/* 头文件包含 ----------------------------------------------------------------*/
#include "sys.h"
#include "board_config.h"

/**
  * @brief  系统时钟配置
  * @param  无
  * @retval 无
  */ 
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

#ifdef DEV_BOARD_MODE
    /* 开发板模式：25MHz晶振配置 */
    /* 使用PLL2方案：25MHz -> PREDIV2(/5) -> 5MHz -> PLL2(*8) -> 40MHz -> PREDIV1(/5) -> 8MHz -> PLL(*9) -> 72MHz */
    
    /* 1. 配置HSE、PLL2和PLL */
    RCC_OscInitStruct.OscillatorType  = RCC_OSCILLATORTYPE_HSE;
    RCC_OscInitStruct.HSEState        = RCC_HSE_ON;
    RCC_OscInitStruct.HSEPredivValue  = RCC_HSE_PREDIV_DIV5;   // PREDIV1: 40MHz / 5 = 8MHz
    RCC_OscInitStruct.HSIState        = RCC_HSI_ON;
    RCC_OscInitStruct.Prediv1Source   = RCC_PREDIV1_SOURCE_PLL2;  // PREDIV1输入来自PLL2
    RCC_OscInitStruct.PLL.PLLState    = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource   = RCC_PLLSOURCE_HSE;
    RCC_OscInitStruct.PLL.PLLMUL      = RCC_PLL_MUL9;          // 8MHz * 9 = 72MHz
    
    /* 配置PLL2 */
    RCC_OscInitStruct.PLL2.PLL2State  = RCC_PLL2_ON;
    RCC_OscInitStruct.PLL2.PLL2MUL    = RCC_PLL2_MUL8;         // 5MHz * 8 = 40MHz
    RCC_OscInitStruct.PLL2.HSEPrediv2Value = RCC_HSE_PREDIV2_DIV5; // 25MHz / 5 = 5MHz
    
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
        Error_Handler(__FILE__, __LINE__);
        
#else
    /* 产品模式：8MHz晶振配置 */
    /* 直接使用PLL：8MHz -> PLL -> 72MHz */
    
    /* 1. 配置HSE和PLL */
    RCC_OscInitStruct.OscillatorType  = RCC_OSCILLATORTYPE_HSE;
    RCC_OscInitStruct.HSEState        = RCC_HSE_ON;
    RCC_OscInitStruct.HSEPredivValue  = RCC_HSE_PREDIV_DIV1;   // 8MHz / 1 = 8MHz
    RCC_OscInitStruct.HSIState        = RCC_HSI_ON;
    RCC_OscInitStruct.Prediv1Source   = RCC_PREDIV1_SOURCE_HSE;
    RCC_OscInitStruct.PLL.PLLState    = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource   = RCC_PLLSOURCE_HSE;
    RCC_OscInitStruct.PLL.PLLMUL      = RCC_PLL_MUL9;          // 8MHz * 9 = 72MHz
    RCC_OscInitStruct.PLL2.PLL2State  = RCC_PLL_NONE;
    
    if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
        Error_Handler(__FILE__, __LINE__);
#endif

    /* 2. 配置总线时钟 */
    RCC_ClkInitStruct.ClockType      = RCC_CLOCKTYPE_HCLK  | RCC_CLOCKTYPE_SYSCLK |
                                       RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
    RCC_ClkInitStruct.SYSCLKSource   = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.AHBCLKDivider  = RCC_SYSCLK_DIV1;   // 72 MHz
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;     // 36 MHz (CAN使用此时钟)
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;     // 72 MHz

    if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
        Error_Handler(__FILE__, __LINE__);
    
    /* 时钟配置完成：
     * 系统时钟 = 72MHz
     * APB1 = 36MHz (CAN时钟源)
     * APB2 = 72MHz
     * CAN波特率 = APB1 / 分频器 / (1 + BS1 + BS2)
     * 500kbps = 36MHz / 8 / (1 + 6 + 2) = 500kHz
     */
}

/**
  * @brief  参数检查函数
  * @param  *file -- 错误文件名
  * @param  line -- 代码行号
  * @retval 无
  */
void Error_Handler(char *file, uint32_t line)
{
	// 用户可以添加自己的代码报告源代码文件名和代码行号，比如将错误文件和行号打印到串口
	// printf("Wrong parameters value: file %s on line %d\r\n", file, line)
	
	// 这是一个死循环，断言失败时程序会在此处死机，以便于用户查错
	if (line == 0)
	{
		return ;
	}
	
	while (1);
}


/*********************************************END OF FILE**********************/

