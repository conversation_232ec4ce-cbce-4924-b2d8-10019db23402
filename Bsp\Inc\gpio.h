#ifndef _GPIO_H
#define _GPIO_H

/* 头文件包含 ----------------------------------------------------------------*/
#include "stm32f1xx_hal.h"  
#include "sys.h"

/* 宏定义 --------------------------------------------------------------------*/
#define MARK0	PCin(5)
#define MARK1	PBin(0)
#define MARK2	PBin(10)
#define MARK3	PCin(7)
#define MARK4	PCin(8)
#define MARK5	PCin(9)
#define MARK6	PAin(8)

/* 函数声明 ------------------------------------------------------------------*/ 
void <PERSON><PERSON>(void);
uint8_t <PERSON><PERSON><PERSON>ddr(void);

#endif 

/*********************************************END OF FILE**********************/
