#ifndef _GPIO_H
#define _GPIO_H

/* 头文件包含 ----------------------------------------------------------------*/
#include "stm32f1xx_hal.h"  
#include "sys.h"

/* 宏定义 --------------------------------------------------------------------*/
#define MARK0	PBin(11)
#define MARK1	PBin(10)
#define MARK2	PBin(2)
#define MARK3	PBin(1)
#define MARK4	PBin(0)
#define MARK5	PCin(5)
#define MARK6	PCin(4)

/* 函数声明 ------------------------------------------------------------------*/ 
void <PERSON><PERSON>(void);
uint8_t <PERSON><PERSON><PERSON>dd<PERSON>(void);

#endif 

/*********************************************END OF FILE**********************/
