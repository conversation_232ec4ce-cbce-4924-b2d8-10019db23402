#ifndef __I2C_H
#define __I2C_H

#include "stm32f1xx_hal.h"
#include "gpio.h"
#include "delay.h"

// 软件I2C引脚定义
#define I2C_SCL_PIN     GPIO_PIN_6
#define I2C_SCL_PORT    GPIOB
#define I2C_SDA_PIN     GPIO_PIN_12
#define I2C_SDA_PORT    GPIOC

// I2C时序延时（使用简单循环延时，与C51代码保持一致）
// STM32F107运行在72MHz，比C51快得多，需要更长的延时
// 经测试，1000次循环约等于10us延时
#define I2C_DELAY()     do { for(volatile uint32_t i = 0; i < 1000; i++); } while(0)

// 软件I2C函数声明
void Soft_I2C_Init(void);
void Soft_I2C_Start(void);
void Soft_I2C_Stop(void);
void Soft_I2C_SendByte(uint8_t byte);
uint8_t Soft_I2C_ReadByte(uint8_t ack);
uint8_t Soft_I2C_WaitAck(void);
void Soft_I2C_Ack(void);
void Soft_I2C_NAck(void);

// 高层I2C操作函数
HAL_StatusTypeDef Soft_I2C_Master_Transmit(uint16_t DevAddress, uint8_t *pData, uint16_t Size);
HAL_StatusTypeDef Soft_I2C_Master_Receive(uint16_t DevAddress, uint8_t *pData, uint16_t Size);
HAL_StatusTypeDef Soft_I2C_Mem_Write(uint16_t DevAddress, uint16_t MemAddress, uint16_t MemAddSize, uint8_t *pData, uint16_t Size);
HAL_StatusTypeDef Soft_I2C_Mem_Read(uint16_t DevAddress, uint16_t MemAddress, uint16_t MemAddSize, uint8_t *pData, uint16_t Size);

#endif /* __I2C_H */
