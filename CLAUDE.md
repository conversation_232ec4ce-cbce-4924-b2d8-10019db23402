# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build System

This is a Keil uVision project for STM32F107VC microcontroller:
- **IDE**: Keil uVision (project file: `Project/Template.uvprojx`)
- **Compiler**: ARM Compiler 6 (ARMCLANG V6.21)
- **Target MCU**: GD32F107RCT6 (Cortex-M3, 256KB Flash, 96KB RAM) - Using STM32 HAL Library
- **HAL Version**: STM32F1xx HAL Driver

### Build Commands

Since this is a Keil project, building must be done through the Keil IDE:
1. Open `Project/Template.uvprojx` in Keil uVision
2. Build: Project → Build Target (F7)
3. Clean: Project → Clean Targets
4. Flash: Flash → Download (F8)

To clean build artifacts from command line:
```bash
cd /mnt/d/MW_Work/STM32F107/MCU/STM32F107_Template
./keilkilll.bat  # Windows batch file that removes all build artifacts
```

### Debug Configuration
- Debugger: J-Link (SWD protocol)
- Debug settings are in `Project/Template.uvoptx`

## Architecture Overview

### Project Structure
```
STM32F107_Template/
├── User/              # Main application code
│   ├── main.c         # Application entry point and main loop
│   ├── stm32f1xx_it.c # Interrupt handlers
│   └── stm32f1xx_hal_conf.h # HAL configuration
├── Bsp/               # Board Support Package - hardware abstraction
│   ├── Inc/           # BSP headers
│   └── Src/           # BSP implementations
├── Libraries/         # STM32 HAL and CMSIS libraries
├── Doc/               # Documentation including 通信协议.md
└── Project/           # Keil project files and build outputs
```

### Key Components

1. **Main Application Flow** (`User/main.c`):
   - HAL initialization and 72MHz clock configuration
   - Peripheral initialization sequence:
     - ADC1 initialization
     - SPI1 (connects to CPLD-A)
     - SPI3 (connects to CPLD-B)
   - CPLD initialization with specific command sequence
   - Mark address acquisition after 1.5s delay
   - CAN1 and CAN2 initialization
   - Main loop processes CAN messages from both interfaces

2. **Communication Interfaces**:
   - **CAN**: Dual CAN interfaces (CAN1, CAN2) for primary communication
   - **SPI**: Two SPI interfaces (SPI1, SPI3) for CPLD communication
   - **UART**: Serial communication support
   - **ADC**: ADC1 for analog measurements

3. **BSP Modules** (`Bsp/`):
   - `CanDataProcess`: Handles incoming CAN data processing based on mark address
   - `STM32Flash`: Internal flash memory operations
   - `gpio`, `can`, `spi`, `usart`, `adc`: Hardware peripheral drivers
   - `delay`: Millisecond/microsecond delay functions
   - `sys`: System-level configurations

### Important Notes

- The code uses Chinese comments (now converted to UTF-8 encoding)
- CAN data buffers are 256 bytes each for CAN1 and CAN2
- The system uses a "Mark Address" for device identification
- CPLD initialization requires specific timing (100ms power stabilization, 1.5s mark address stabilization)

## Recent Updates (2025-01-28)

### 1. CAN GPIO Pin Configuration Fix
Fixed CAN initialization error by correcting GPIO pin assignments in `can.c`:
- **CAN1**: Changed from PA11/PA12 to PD0/PD1 (with AFIO remap)
- **CAN2**: Changed from PB12/PB13 to PB5/PB6 (with AFIO remap)
- Added AFIO clock enable and proper remapping macros
- Added CAN1 clock enable for CAN2 (required dependency)
- CAN baud rate: 500 Kbps (36MHz APB1 / 8 / 9)

### 2. FPGA Work Mode Control Logic Fix
Corrected the work mode mapping in `fpga_ctrl.c`:
- CAN protocol: 0 = Normal mode, 1 = Low-power mode
- FPGA TD bit: 1 = Power ON (Normal), 0 = Power OFF (Low-power)
- Added logic inversion: `(mode == 0) ? 1 : 0`

### 3. Product Information Enhancement
Enhanced product information handling in `CanDataProcess.c`:

**Product info storage structure:**
- `manufacturerCode`: 0x00 (十所)
- `productYear/Month/Day`: BCD format production date
- `serialYear/Batch/Num`: BCD format serial number

**0xF000 Response format improvements:**
- Proper BCD encoding for dates and serial numbers
- Working frequency range: 30MHz-3GHz (30000-3000000 kHz)
- MARK address from hardware pins

**Added debug command 0xF00E:**
- Allows setting product info via CAN for testing
- Format: [0xF0][0x0E][ManufCode][Year_H][Year_L][Month][Day][SerialYear_H][SerialYear_L][Batch][SerialNum_H][SerialNum_L]
- Saves to Flash at STORE_ADDR (0x3F800)

### 4. Code Quality Improvements
- Fixed all UTF-8 encoding issues in source files
- Corrected function names (STMFLASH_Write vs Stm32Flash_Write)
- Added proper error handling in CAN initialization

## Recent Updates (2025-02-07)

### 1. Board Configuration Management System
Created unified board configuration system to manage differences between development board and product:
- **New file**: `Bsp/Inc/board_config.h` - centralized configuration for hardware differences
- **Macro definition**: `DEV_BOARD_MODE` - switch between development board and product mode
- **Features managed**:
  - Crystal frequency (25MHz for dev board, 8MHz for product)
  - CAN pin configuration
  - PLL clock configuration
  - CAN2 initialization control

### 2. CAN Pin Configuration with Mode Switching
Updated CAN driver to support both development board and product configurations:
- **Files modified**: `can.c`, `can.h`
- **Development board mode** (when `DEV_BOARD_MODE` is defined):
  - CAN1: PD0(RX), PD1(TX) - with AFIO remap
  - CAN2: PB5(RX), PB6(TX) - with AFIO remap
  - CAN2 initialization skipped in main.c
- **Product mode** (when `DEV_BOARD_MODE` is not defined):
  - CAN1: PA11(RX), PA12(TX) - default pins
  - CAN2: PB12(RX), PB13(TX) - default pins
  - Both CAN1 and CAN2 initialized

### 3. System Clock Configuration for Dual Crystal Support
Modified system clock configuration to support both 25MHz and 8MHz crystals:
- **Files modified**: `sys.c`, `sys.h`
- **Development board**: 25MHz → PLL2 → PREDIV1 → PLL → 72MHz
  - Clock path: 25MHz/5 → 5MHz (PLL2 input) → 5MHz*8 = 40MHz (PLL2 output) → 40MHz/5 = 8MHz (PREDIV1 output) → 8MHz*9 = 72MHz
- **Product**: 8MHz → PLL → 72MHz
  - Clock path: 8MHz/1 → 8MHz → 8MHz*9 = 72MHz
- Both configurations produce exact 72MHz for accurate CAN baud rate

### 4. Main Program Updates
- **Files modified**: `main.c`, `main.h`
- Added conditional compilation to skip CAN2 initialization in development board mode
- Fixed encoding issues (converted to UTF-8)
- Included `board_config.h` for mode switching

### 5. UART Driver Modification
Modified UART driver from USART1 to USART2 for GD32F107RCT6 compatibility:
- **Files modified**: `usart.h`, `usart.c`
- **Pin changes**: PA9/PA10 (USART1) → PA2/PA3 (USART2)
- **DMA channel**: DMA1_Channel5 → DMA1_Channel6 (for USART2 RX)
- **Function rename**: `Usart1_Init()` → `Usart2_Init()`
- **Handle rename**: `huart1` → `huart2`
- **IRQ handler**: `USART1_IRQHandler()` → `USART2_IRQHandler()`

### 6. MCU Target Update
- Changed target MCU from STM32F107VC to GD32F107RCT6
- Using STM32 HAL library with GD32 chip (already adapted and working well)
- RAM increased from 64KB to 96KB

## Recent Updates (2025-01-27)

### 1. GPIO Pin Configuration Update
Updated MARK pin definitions in `gpio.h` and `gpio.c`:
- MARK0: PB11
- MARK1: PB10  
- MARK2: PB2
- MARK3: PB1
- MARK4: PB0
- MARK5: PC5
- MARK6: PC4

### 2. CAN Communication Protocol Implementation
Completely rewrote `CanDataProcess.c` and `CanDataProcess.h` based on `Doc/通信协议.md`:

**Implemented message types:**
- 0xF000: Module product info query
- 0xF001: Module BIT query
- 0xF002: Channel parameters query
- 0xF004: Channel parameters setting
- 0xAA55: Module initialization

**Removed single-channel control features:**
- 0xF00A: Internal function A (flatness table query) - REMOVED
- 0xF00B: Internal function B (flatness table modification) - REMOVED

**Key design changes:**
- Changed from 10 individual channels to 2 channel groups
- Channel 0: All channels (both groups)
- Channel 1: Channels 1-5 (group 0)
- Channel 2: Channels 6-10 (group 1)
- Single channel control commands (channel 3-12) are ignored

**Key data structures:**
- `ChannelParams_t`: Stores channel group parameters (frequency, attenuation, bandwidth, work mode)
- `channelGroupParams[2]`: Array storing parameters for 2 channel groups

**Protocol parameters parsed:**
- Frequency: 30000-3000000 kHz (1kHz precision)
- RF Attenuation: 0-7 (5dB steps, 0-35dB range)
- IF Attenuation: 0-30 (1dB steps)
- Bandwidth: 3 levels (200kHz, 5MHz, 50MHz)
- Work Mode: Normal/Low-power

### 3. FPGA Control Implementation
Created separate FPGA control module (`fpga_ctrl.c/h`):

**Key features:**
- Uses SPI3 (PA15-NSS, PB3-SCK, PB4-MISO, PB5-MOSI) for MCU-FPGA communication
- Implements 10-byte communication protocol per `Doc/FPGA - MCU 通信协议.md`
- Channel numbers consistent with CAN protocol (0/1/2)
- Automatic frequency band selection based on `Doc/开关滤波器衰减器真值表.xlsx`

**Implemented control functions:**
- `FPGA_SetBandwidth()`: Controls bandwidth filters (200kHz/5MHz/50MHz)
- `FPGA_SetWorkMode()`: Controls power mode (Normal/Low-power)
- `FPGA_SetRfAtten()`: Controls RF attenuation (0-35dB, 5dB steps)
- `FPGA_SetIfAtten()`: Controls IF attenuation (0-30dB, 1dB steps)
- `FPGA_SetCalAtten()`: Controls calibration attenuation (0-31.5dB, 0.5dB steps)
- `FPGA_SetFrequencyBand()`: Automatically selects filters based on frequency
- `FPGA_UpdateChannelParams()`: Updates all parameters for a channel group
- `FPGA_SendBatchCommands()`: Optimized batch command sending for minimal latency

**Frequency band switching logic:**
- 30-200MHz: Direct path (switch=0)
- 200-300MHz: Filter V1=0,V2=1,V3=0,V4=0,V5=1
- 300-450MHz: Filter V1=1,V2=0,V3=0,V4=1,V5=0
- 450-700MHz: Filter V1=0,V2=1,V3=1,V4=0,V5=0
- 700MHz-1.1GHz: Filter V1=1,V2=0,V3=0,V4=0,V5=1
- 1.1-1.8GHz: Filter V1=0,V2=1,V3=0,V4=1,V5=0
- 1.8-3GHz: Filter V1=1,V2=0,V3=1,V4=0,V5=0

### 4. Character Encoding Fix
Converted source files from GB2312 to UTF-8 encoding for better compatibility:
- `CanDataProcess.c`
- `CanDataProcess.h`
- `gpio.c`
- `gpio.h`

## Architecture Notes

### Group-based Channel Control
The system uses group-based control exclusively:
- Hardware can only control channels in groups (1-5 and 6-10)
- CAN protocol channel numbers: 0 (all), 1 (group 1-5), 2 (group 6-10)
- FPGA communication uses the same channel numbering
- Single channel commands are ignored at the CAN protocol level

### SPI Communication
- MCU acts as SPI master, FPGA as slave
- SPI1 configuration: 1MHz clock, CPOL=0, CPHA=0
- 10-byte transaction: 5-byte command + 5x 0xFF for response
- Response received during the last 5 bytes

## Usage Instructions

### Switching Between Development Board and Product Mode

To switch between development board and product mode, edit `Bsp/Inc/board_config.h`:

**For Development Board Mode** (default):
```c
#define DEV_BOARD_MODE  // Keep this line
```

**For Product Mode**:
```c
// #define DEV_BOARD_MODE  // Comment out this line
```

### Key Differences Between Modes

| Feature | Development Board Mode | Product Mode |
|---------|------------------------|--------------|
| Crystal Frequency | 25MHz | 8MHz |
| System Clock | 72MHz (via PLL2) | 72MHz (direct PLL) |
| CAN1 Pins | PD0/PD1 (remapped) | PA11/PA12 (default) |
| CAN2 Pins | PB5/PB6 (remapped) | PB12/PB13 (default) |
| CAN2 Init | Skipped | Enabled |

## Recent Updates (2025-02-07) - SPI Optimization

### 1. SPI Migration from SPI1 to SPI3
- **Removed SPI1**: Deleted all SPI1-related code from `spi.c`, `spi.h`, and `main.c`
- **Updated FPGA control**: Modified `fpga_ctrl.c` to use SPI3 exclusively
- **Pin configuration**: PA15 (NSS), PB3 (SCK), PB4 (MISO), PB5 (MOSI)

### 2. SPI Communication Optimization
Fixed SPI communication issues and optimized performance:

**Fixed RXNE hanging issue:**
- Added `__HAL_SPI_ENABLE(&hspi3)` after initialization
- Properly configured MISO pin (PB4) as input with pull-up
- Set SSI bit in software NSS mode for proper master operation

**Optimized transmission speed:**
- Implemented direct register access for SPI data transfer
- Used "staggered read" technique to avoid RXNE waiting
- Reduced byte-to-byte interval to near zero within a command

**Batch command optimization:**
- Created `FPGA_SendBatchCommands()` for sending multiple commands efficiently
- Single CS assertion for entire batch (5 commands = 50 bytes)
- Reduced inter-command gap from ~3µs to minimal overhead
- All parameter calculations inlined in `FPGA_UpdateChannelParams()`

**Performance improvements:**
- 10-byte command transmission is now continuous with no gaps
- Multiple commands sent with minimal inter-command delay
- Achieved near-theoretical maximum speed for software SPI implementation

## Recent Updates (2025-02-07) - CAN Protocol Rewrite

### 1. Complete CAN Data Processing Rewrite
Rewrote `CanDataProcess.c` and `CanDataProcess.h` based on `Doc/通信协议-模块.md`:

**New data structures:**
- `LowChannel_t`: Low-band local oscillator channel parameters (frequency, work mode)
- `HighChannel_t`: High-band local oscillator channel parameters (frequency only)
- `ModuleParams_t`: Complete module parameters including all channels
- `ProductInfo_t`: Product information structure (manufacturer, dates, serial)
- `BitInfo_t`: BIT information structure (temperature, voltages, lock status)

**Implemented CAN commands (0xF0 message category):**
- **0x00**: Product information query
  - Returns manufacturer code, production date (BCD), serial number (BCD)
  - Returns frequency ranges for all oscillators
- **0x01**: BIT query
  - Returns temperature, voltages, lock status for all channels
- **0x02**: Channel parameters query
  - Supports individual channel and all-channel queries
  - Returns frequency and work mode for each channel
- **0x04**: Channel parameters setting
  - Supports individual channel and all-channel settings
  - Validates frequency ranges before setting

**Key improvements:**
- Support for 9 independent channels (Low1/2, High1/2/3 with dual bands)
- Frequency range validation (Low: 30-3000MHz, High: 2-18GHz)
- BCD format for dates and serial numbers
- Modular design with separate functions for each command type

### 2. Variable Substitution for Hardware Interface
Created variables to replace actual hardware communication:
- `canTxBuffer[]`: Stores CAN response data to be sent
- `canTxLength`: Length of CAN response data
- `fpgaCommandBuffer[]`: Stores FPGA commands to be sent
- `fpgaCommandLength`: Length of FPGA command data

These variables allow the CAN parsing logic to be tested independently of hardware interfaces.

### 3. FPGA Control Module Status
- `fpga_ctrl.c` temporarily disabled in project due to lack of FPGA control logic specification
- The file remains in the codebase but is excluded from compilation
- FPGA command generation logic is implemented in `CanDataProcess.c` for future use

### 4. Main Program Integration
- Added `InitModuleParams()` call in `main.c` to initialize module parameters
- Module parameters are initialized with default values on startup

## Recent Updates (2025-01-07) - CAN Response Function Implementation

### SendCanResponse Function Enhancement
Completed the implementation of `SendCanResponse` function in `CanDataProcess.c`:

**Changes made:**
- Modified `SendCanResponse()` to actually call `CANxSend()` function
- Uses `DATA_INQUIRE` (0x04) priority for query response messages
- Added automatic buffer length reset after sending
- Verified all query functions properly call `SendCanResponse()`

**Implementation details:**
```c
static void SendCanResponse(uint8_t MarkAddr)
{
    if (canTxLength > 0) {
        // 实际发送CAN数据
        // 使用查询应答数据的优先级(DATA_INQUIRE = 0x04)
        CANxSend(canTxBuffer, canTxLength, DATA_INQUIRE, MarkAddr);
        
        // 清空发送缓冲区长度，为下次发送做准备
        canTxLength = 0;
    }
}
```

**Functions using SendCanResponse:**
- `ProcessProductInfoQuery()`: Sends product information response
- `ProcessBitQuery()`: Sends BIT status response  
- `ProcessParamQuery()`: Sends channel parameters response
- `ProcessParamSet()`: No response needed (per protocol specification)

## Next Steps
- Add flash storage for channel parameters persistence
- Implement proper error handling and parameter validation
- Add CAN response messages for parameter setting confirmations
- Implement actual hardware initialization sequences
- Add temperature and voltage monitoring functions
- Define and implement FPGA control protocol when specifications are available