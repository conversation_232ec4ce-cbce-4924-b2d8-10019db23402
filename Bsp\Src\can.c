/**
  ******************************************************************************
  * @file       : can.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : CAN总线驱动实现
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */
  
/* 头文件包含 ----------------------------------------------------------------*/
#include "can.h"
#include "sys.h"
#include "string.h"
#include "delay.h"

/* 变量定义 -------------------------------------------------------------------*/
CAN_HandleTypeDef hcan1;	// CAN1句柄
CAN_HandleTypeDef hcan2;	// CAN2句柄
uint8_t Can1RxData[256];	// CAN接收数据
uint8_t Can2RxData[256];	// CAN接收数据
uint16_t Can1RxCpt = 0;		// CAN1数据接收完成
uint16_t Can2RxCpt = 0;		// CAN1数据接收完成
CAN_IdTypeDef CanIdConfig;	// CAN发送ID 


/**
  * @brief  CAN1初始化
  * @param  无
  * @retval 无
  * @note   BoundRate = APB1(36MHz) / (TimeSeg1 + TimeSeg2 + SyncJumpWidth) / Prescaler
  */
void CAN1_Init(uint16_t RxID)
{
	CAN_FilterTypeDef  sFilterConfig;
	uint16_t MaskIdLow = (SADDR<<11) + (RxID<<3) + 0x04;
	
	#ifdef _DEBUG
		MaskIdLow = 0x0000;						//正式程序需注释
	#endif

	// CAN初始化
	hcan1.Instance = CAN1;								// CAN1
	hcan1.Init.Prescaler = 8;							// 波特率分频器	
	hcan1.Init.Mode = CAN_MODE_NORMAL;					// CAN模式 -- 正常模式
	hcan1.Init.SyncJumpWidth = CAN_SJW_1TQ;				// 重新同步跳跃宽度
	hcan1.Init.TimeSeg1 = CAN_BS1_6TQ;					// 时间段1时间单元
	hcan1.Init.TimeSeg2 = CAN_BS2_2TQ;					// 时间段2时间单元
	hcan1.Init.TimeTriggeredMode = DISABLE;				// 时间触发通信模式 -- 关闭
	hcan1.Init.AutoBusOff = ENABLE;						// 自动离线管理 -- 开启
	hcan1.Init.AutoWakeUp = DISABLE;					// 自动唤醒模式 -- 关闭
	hcan1.Init.AutoRetransmission = ENABLE;				// 报文自动重传 -- 开启
	hcan1.Init.ReceiveFifoLocked = DISABLE;				// 接收FIFO锁定 -- 关闭 (新数据会覆盖老数据)
	hcan1.Init.TransmitFifoPriority = DISABLE;			// FIFO传输优先级使能-- 关闭
	if (HAL_CAN_Init(&hcan1) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
	
	// CAN滤波器0设置 -- 接收本机地址消息
	sFilterConfig.FilterBank = 0;						// 过滤器组选择 (0-27)
	sFilterConfig.FilterMode = CAN_FILTERMODE_IDMASK;	// 过滤器模式 -- 标识符掩码模式
	sFilterConfig.FilterScale = CAN_FILTERSCALE_32BIT;	// 过滤器位宽 -- 单个32位
	sFilterConfig.FilterIdHigh = 0x0000;				// 过滤器ID高位
	sFilterConfig.FilterIdLow = MaskIdLow;				// 过滤器ID低位
	sFilterConfig.FilterMaskIdHigh = 0x0000;			// 过滤器掩码高位
	sFilterConfig.FilterMaskIdLow = 0x7FFF;				// 过滤器掩码低位
	#ifdef _DEBUG
	sFilterConfig.FilterMaskIdLow = 0x0000;				// 过滤器掩码低位
	#endif
	sFilterConfig.FilterFIFOAssignment = CAN_RX_FIFO0;	// 过滤器FIFO关联 -- FIFO0
	sFilterConfig.FilterActivation = ENABLE;			// 使能过滤器
	sFilterConfig.SlaveStartFilterBank = 0;				// 启动过滤器组
	if (HAL_CAN_ConfigFilter(&hcan1, &sFilterConfig) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
	
	// 启动CAN
	if (HAL_CAN_Start(&hcan1) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
	
	// 启动中断
	if (HAL_CAN_ActivateNotification(&hcan1, CAN_IT_RX_FIFO0_MSG_PENDING) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
}

/**
  * @brief  CAN2初始化
  * @param  无
  * @retval 无
  * @note   BoundRate = APB1(36MHz) / (TimeSeg1 + TimeSeg2 + SyncJumpWidth) / Prescaler
  */
void CAN2_Init(uint16_t RxID)
{
	CAN_FilterTypeDef  sFilterConfig;
	uint16_t MaskIdLow = (SADDR<<11) + (RxID<<3) + 0x04;
	
	#ifdef _DEBUG
		MaskIdLow = 0x0000;
	#endif
	
	// CAN初始化
	hcan2.Instance = CAN2;								// CAN1
	hcan2.Init.Prescaler = 8;							// 波特率分频器	
	hcan2.Init.Mode = CAN_MODE_NORMAL;					// CAN模式 -- 正常模式
	hcan2.Init.SyncJumpWidth = CAN_SJW_1TQ;				// 重新同步跳跃宽度
	hcan2.Init.TimeSeg1 = CAN_BS1_6TQ;					// 时间段1时间单元
	hcan2.Init.TimeSeg2 = CAN_BS2_2TQ;					// 时间段2时间单元
	hcan2.Init.TimeTriggeredMode = DISABLE;				// 时间触发通信模式 -- 关闭
	hcan2.Init.AutoBusOff = ENABLE;						// 自动离线管理 -- 开启
	hcan2.Init.AutoWakeUp = DISABLE;					// 自动唤醒模式 -- 关闭
	hcan2.Init.AutoRetransmission = ENABLE;				// 报文自动重传 -- 开启
	hcan2.Init.ReceiveFifoLocked = DISABLE;				// 接收FIFO锁定 -- 关闭 (新数据会覆盖老数据)
	hcan2.Init.TransmitFifoPriority = DISABLE;			// FIFO传输优先级使能-- 关闭
	if (HAL_CAN_Init(&hcan2) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
	
	// CAN滤波器0设置 -- 接收本机地址消息
	sFilterConfig.FilterBank = 14;						// 过滤器组选择 (0-27)
	sFilterConfig.FilterMode = CAN_FILTERMODE_IDMASK;	// 过滤器模式 -- 标识符掩码模式
	sFilterConfig.FilterScale = CAN_FILTERSCALE_32BIT;	// 过滤器位宽 -- 单个32位
	sFilterConfig.FilterIdHigh = 0x0000;				// 过滤器ID高位
	sFilterConfig.FilterIdLow = MaskIdLow;				// 过滤器ID低位
	sFilterConfig.FilterMaskIdHigh = 0x0000;			// 过滤器掩码高位
	sFilterConfig.FilterMaskIdLow = 0x7FFF;				// 过滤器掩码低位
	#ifdef _DEBUG
	sFilterConfig.FilterMaskIdLow = 0x0000;				// 过滤器掩码低位
	#endif
	sFilterConfig.FilterFIFOAssignment = CAN_RX_FIFO0;	// 过滤器FIFO关联 -- FIFO0
	sFilterConfig.FilterActivation = ENABLE;			// 使能过滤器
	sFilterConfig.SlaveStartFilterBank = 14;			// 启动过滤器组
	if (HAL_CAN_ConfigFilter(&hcan2, &sFilterConfig) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
	
	// 启动CAN
	if (HAL_CAN_Start(&hcan2) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
	
	// 启动中断
	if (HAL_CAN_ActivateNotification(&hcan2, CAN_IT_RX_FIFO0_MSG_PENDING) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
}

/**
  * @brief  CAN数据发送
  * @param  SendData : 串口句柄
  * @retval 无
  * @note   此函数会被 HAL_CAN_Init 调用
  */
void CANxSend(uint8_t SendData[], uint8_t SendLength, uint8_t Priority, uint8_t MarkAddr)
{	
	CAN_TxHeaderTypeDef TxHeader;
	uint8_t TxData[8], i;
	uint32_t TxMailbox, ExtId;
	
	// 标识符计算 优先级(3) + 平台类型(2，平台为LRM(3)) + 源地址 + 目的地址
	ExtId = (Priority<<26) + (PLATFORM_LRM<<24) + (SADDR<<20) + (MarkAddr<<12) + (CanIdConfig.DestinationAddr&0xFFF);
	
	TxHeader.ExtId = ExtId; // 标识符
	TxHeader.IDE = CAN_ID_EXT; // 扩展帧
	TxHeader.RTR = CAN_RTR_DATA; // 数据帧
	
	for (i = 0; i < SendLength; i += 8)
	{
		if (SendLength - i > 8)
		{
			TxHeader.DLC = 0x08;
			memcpy(TxData, &SendData[i], TxHeader.DLC);
		}
		else
		{
			TxHeader.DLC = SendLength - i;
			memcpy(TxData, &SendData[i], TxHeader.DLC);
		}
        
        if (CanIdConfig.CanChoose == 0x01)
        {
            HAL_CAN_AddTxMessage(&hcan1, &TxHeader, TxData, &TxMailbox);
            // 等待发送完成
            while(HAL_CAN_IsTxMessagePending(&hcan1, TxMailbox));
        }
		else
        {
            HAL_CAN_AddTxMessage(&hcan2, &TxHeader, TxData, &TxMailbox);
            // 等待发送完成
            while(HAL_CAN_IsTxMessagePending(&hcan2, TxMailbox));
        }
	}
}

/**
  * @brief  CAN底层初始化函数
  * @param  hcan : 串口句柄
  * @retval 无
  * @note   此函数会被 HAL_CAN_Init 调用
  */
void HAL_CAN_MspInit(CAN_HandleTypeDef* hcan)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	
	if(hcan->Instance == CAN1)
	{
		__HAL_RCC_CAN1_CLK_ENABLE();
		
#ifdef DEV_BOARD_MODE
		// 开发板模式：CAN1使用PD0/PD1
		__HAL_RCC_GPIOD_CLK_ENABLE();
		__HAL_RCC_AFIO_CLK_ENABLE();  // 使能AFIO时钟用于重映射
		
		// CAN1重映射到PD0/PD1
		__HAL_AFIO_REMAP_CAN1_3();  // CAN1_RX映射到PD0, CAN1_TX映射到PD1
		
		/**CAN1 GPIO 配置
		PD0     ------> CAN1_RX
		PD1     ------> CAN1_TX
		*/
		GPIO_InitStruct.Pin = GPIO_PIN_0;
		GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
		GPIO_InitStruct.Pull = GPIO_NOPULL;
		HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

		GPIO_InitStruct.Pin = GPIO_PIN_1;
		GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
		HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
#else
		// 产品模式：CAN1使用PA11/PA12
		__HAL_RCC_GPIOA_CLK_ENABLE();
		
		/**CAN1 GPIO 配置
		PA11     ------> CAN1_RX
		PA12     ------> CAN1_TX
		*/
		GPIO_InitStruct.Pin = GPIO_PIN_11;
		GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
		GPIO_InitStruct.Pull = GPIO_NOPULL;
		HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

		GPIO_InitStruct.Pin = GPIO_PIN_12;
		GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
		HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
#endif
		
		HAL_NVIC_SetPriority(CAN1_RX0_IRQn, 0, 0);
		HAL_NVIC_EnableIRQ(CAN1_RX0_IRQn);
	}
	else if (hcan->Instance == CAN2)
	{
		__HAL_RCC_CAN1_CLK_ENABLE();  // CAN2需要先使能CAN1时钟
		__HAL_RCC_CAN2_CLK_ENABLE();
		__HAL_RCC_GPIOB_CLK_ENABLE();
		
#ifdef DEV_BOARD_MODE
		// 开发板模式：CAN2使用PB5/PB6
		__HAL_RCC_AFIO_CLK_ENABLE();  // 使能AFIO时钟用于重映射
		
		// CAN2重映射到PB5/PB6
		__HAL_AFIO_REMAP_CAN2_ENABLE();  // CAN2_RX映射到PB5, CAN2_TX映射到PB6
		
		/**CAN2 GPIO 配置
		PB5     ------> CAN2_RX
		PB6     ------> CAN2_TX
		*/
		GPIO_InitStruct.Pin = GPIO_PIN_5;
		GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
		GPIO_InitStruct.Pull = GPIO_NOPULL;
		HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

		GPIO_InitStruct.Pin = GPIO_PIN_6;
		GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
		HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
#else
		// 产品模式：CAN2使用PB12/PB13
		/**CAN2 GPIO 配置
		PB12     ------> CAN2_RX
		PB13     ------> CAN2_TX
		*/
		GPIO_InitStruct.Pin = GPIO_PIN_12;
		GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
		GPIO_InitStruct.Pull = GPIO_NOPULL;
		HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

		GPIO_InitStruct.Pin = GPIO_PIN_13;
		GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
		HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
#endif
		
		HAL_NVIC_SetPriority(CAN2_RX0_IRQn, 1, 0);
		HAL_NVIC_EnableIRQ(CAN2_RX0_IRQn);
	}

}

/**
  * @brief  CAN非阻塞模式下 RX FIFO0消息挂起中断
  * @param  CanHandle : CAN句柄
  * @retval 无
  * @note   此函数会被CAN中断函数调用
  */
void HAL_CAN_RxFifo0MsgPendingCallback(CAN_HandleTypeDef *CanHandle)
{
	uint8_t RxData[8], i;
	CAN_RxHeaderTypeDef RxHeader;
	
	// 接收数据
	if (HAL_CAN_GetRxMessage(CanHandle, CAN_RX_FIFO0, &RxHeader, RxData) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
	
	// 判断是哪个CAN收到数据，讲没有收到数据的另一个CAN接收数据计数清0
	if (CanHandle->Instance == CAN1)
	{
		Can2RxCpt = 0x0000;
		for (i = 0; i < RxHeader.DLC; i++)
		{
			Can1RxData[Can1RxCpt++] = RxData[i];
		}
		if (RxHeader.DLC != 8)
		{
			// 记录发送CAN和CAN ID中源地址，将源地址放入发送目的地址
			CanIdConfig.CanChoose = 0x01;
			CanIdConfig.DestinationAddr = (RxHeader.ExtId >>12)&0xFFF;
			// 置位数据发送完成
			Can1RxCpt |= 0x8000;
		}
		
	}
	else
	{
		Can1RxCpt = 0x0000;
		for (i = 0; i < RxHeader.DLC; i++)
		{
			Can2RxData[Can2RxCpt++] = RxData[i];
		}
		if (RxHeader.DLC != 8)
		{
			// 记录发送CAN和CAN ID中源地址，将源地址放入发送目的地址
			CanIdConfig.CanChoose = 0x02;
			CanIdConfig.DestinationAddr = (RxHeader.ExtId >>12)&0xFFF;
			// 置位数据发送完成
			Can2RxCpt |= 0x8000;
		}
	}
}

/**
  * @brief  CAN1 FIFO0 中断服务函数
  * @param  无
  * @retval 无
  */
void CAN1_RX0_IRQHandler(void)
{
	HAL_CAN_IRQHandler(&hcan1);
}

/**
  * @brief  CAN2 FIFO0 中断服务函数
  * @param  无
  * @retval 无
  */
void CAN2_RX0_IRQHandler(void)
{
	HAL_CAN_IRQHandler(&hcan2);
}

/*********************************************END OF FILE**********************/

