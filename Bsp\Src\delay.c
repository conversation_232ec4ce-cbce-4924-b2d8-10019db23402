/**
  ******************************************************************************
  * @file       : delay.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : 延时函数实现
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */
  
/* 头文件包含 ----------------------------------------------------------------*/
#include "delay.h"

/* 变量定义 ------------------------------------------------------------------*/
static uint32_t fac_us = 0;							//us延时倍乘数

/**
  * @brief  延时初始化
  * @param  SYSCLK:系统时钟频率
  * @retval 无
  */
void delay_init(uint8_t SYSCLK)
{
    HAL_SYSTICK_CLKSourceConfig(SYSTICK_CLKSOURCE_HCLK);//SysTick频率为HCLK
	fac_us = SYSCLK;									//不论是否使用OS,fac_us都需要使用
}

/**
  * @brief  us级延时实现
  * @param  nus : 延时时间(0~190887435,最大值即2^32/fac_us@fac_us=22.5)
  * @retval 无
  */ 
void delay_us(uint32_t nus)
{		
	uint32_t ticks;
	uint32_t told, tnow, tcnt = 0;
	uint32_t reload = SysTick->LOAD;		//LOAD的值	 
	
	ticks = nus * fac_us; 					//需要的节拍数 
	told = SysTick->VAL;        			//刚进入时的计数器值
	
	while(1)
	{
		tnow = SysTick->VAL;	
		if(tnow != told)
		{	    
			if(tnow < told)			//这里注意一下SYSTICK是一个递减的计数器就可以了.
			{
				tcnt += told - tnow;	
			}
			else 
			{
				tcnt += reload - tnow + told;	  
			}				
			told = tnow;
			if(tcnt >= ticks)			//时间超过/等于要延迟的时间,则退出.
			{
				break;
			}
		}  
	};
}

/**
  * @brief  ms级延时实现
  * @param  nms : 延时时间
  * @note	nms <= 1118ms
  * @retval 无
  */
void delay_ms(uint16_t nms)
{
	uint32_t i;
	
	for(i = 0; i < nms; i++) 
	{
		delay_us(1000);
	}
}

/*********************************************END OF FILE**********************/

