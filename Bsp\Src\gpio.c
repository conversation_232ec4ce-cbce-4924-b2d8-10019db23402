/**
  ******************************************************************************
  * @file       : gpio.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : 离散GPIO线控制
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */
  
/* 头文件包含 ----------------------------------------------------------------*/
#include "gpio.h"

/**
  * @brief  离散IO初始化
  * @param  无
  * @retval 无
  */
void GpioInit(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    // 初始化MARK6对应的引脚 (PA8)
    GPIO_InitStruct.Pin = GPIO_PIN_8;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    // 初始化MARK1, MARK2对应的引脚 (PB0, PB10)
    GPIO_InitStruct.Pin = GPIO_PIN_0 | GPIO_PIN_10;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    // 初始化MARK0, MARK3, MARK4, MARK5对应的引脚 (PC5, PC7, PC8, PC9)
    GPIO_InitStruct.Pin = GPIO_PIN_5 | GPIO_PIN_7 | GPIO_PIN_8 | GPIO_PIN_9;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
}

/**
  * @brief  获取MARK地址
  * @param  无
  * @retval MARK地址
  */
uint8_t GetMarkAddr(void)
{
	uint8_t MarkAddr;
	
	MarkAddr = MARK0 + (MARK1<<1) + (MARK2<<2) + (MARK3<<3) + (MARK4<<4) + (MARK5<<5) + (MARK6<<6);
	
	return MarkAddr;
}

/*********************************************END OF FILE**********************/

