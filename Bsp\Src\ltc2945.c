#include "ltc2945.h"
#include "i2c.h"

/**
 * @brief Initialize LTC2945 device
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @retval HAL status
 */
HAL_StatusTypeDef LTC2945_Init(LTC2945_Handle_t *hltc2945)
{
    if (hltc2945 == NULL) {
        return HAL_ERROR;
    }
    
    hltc2945->device_address = LTC2945_I2C_ADDR_7BIT;
    
    // 初始化软件I2C
    Soft_I2C_Init();
    
    // 添加初始化延时，确保LTC2945上电稳定
    HAL_Delay(10);
    
    // Test communication by reading a register
    uint8_t test_data;
    HAL_StatusTypeDef status = LTC2945_ReadRegister(hltc2945, LTC2945_REG_CONTROL_A, &test_data);
    
    if (status == HAL_OK) {
        // 清除故障寄存器，如Linux驱动中所做
        LTC2945_WriteRegister(hltc2945, LTC2945_REG_FAULT, 0x00);
        HAL_Delay(1);
        
        // 设置控制寄存器为正常测量模式
        // Bit 0 = 0: 使用SENSE+/VDD测量功率
        // 其他位保持默认值
        status = LTC2945_WriteRegister(hltc2945, LTC2945_REG_CONTROL_A, 0x00);
    }
    
    return status;
}

/**
 * @brief Read a single register from LTC2945
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @param reg_addr: Register address to read
 * @param data: Pointer to store read data
 * @retval HAL status
 */
HAL_StatusTypeDef LTC2945_ReadRegister(LTC2945_Handle_t *hltc2945, uint8_t reg_addr, uint8_t *data)
{
    // 完全按照C51代码的时序实现
    Soft_I2C_Start();
    
    // 发送写地址0xCC
    Soft_I2C_SendByte(LTC2945_I2C_ADDR_WRITE);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 发送寄存器地址
    Soft_I2C_SendByte(reg_addr);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 重复起始条件
    Soft_I2C_Start();
    
    // 发送读地址0xCF
    Soft_I2C_SendByte(LTC2945_I2C_ADDR_READ);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 读取一个字节，发送NACK
    *data = Soft_I2C_ReadByte(0);
    
    Soft_I2C_Stop();
    
    return HAL_OK;
}

/**
 * @brief Write a single register to LTC2945
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @param reg_addr: Register address to write
 * @param data: Data to write
 * @retval HAL status
 */
HAL_StatusTypeDef LTC2945_WriteRegister(LTC2945_Handle_t *hltc2945, uint8_t reg_addr, uint8_t data)
{
    // 按照C51代码的方式实现
    Soft_I2C_Start();
    
    // 发送写地址0xCC
    Soft_I2C_SendByte(LTC2945_I2C_ADDR_WRITE);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 发送寄存器地址
    Soft_I2C_SendByte(reg_addr);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 发送数据
    Soft_I2C_SendByte(data);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    Soft_I2C_Stop();
    
    return HAL_OK;
}

/**
 * @brief Write control registers A and B (equivalent to LT2945_WriteINT in C51 code)
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @param ctrl_a: Control register A value
 * @param ctrl_b: Control register B value
 * @retval HAL status
 */
HAL_StatusTypeDef LTC2945_WriteControlRegisters(LTC2945_Handle_t *hltc2945, uint8_t ctrl_a, uint8_t ctrl_b)
{
    // 按照C51的LT2945_WriteINT函数实现
    Soft_I2C_Start();
    
    // 发送写地址0xCC
    Soft_I2C_SendByte(LTC2945_I2C_ADDR_WRITE);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 发送寄存器A地址（0x00）
    Soft_I2C_SendByte(0x00);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 发送寄存器A数据
    Soft_I2C_SendByte(ctrl_a);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 发送寄存器B数据
    Soft_I2C_SendByte(ctrl_b);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    Soft_I2C_Stop();
    
    return HAL_OK;
}

/**
 * @brief Write current threshold values (equivalent to LT2945_WriteIThreshold in C51 code)
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @param max_threshold: Maximum current threshold (16-bit value)
 * @param min_threshold: Minimum current threshold (16-bit value)
 * @retval HAL status
 */
HAL_StatusTypeDef LTC2945_WriteCurrentThreshold(LTC2945_Handle_t *hltc2945, uint16_t max_threshold, uint16_t min_threshold)
{
    uint8_t tx_data[4];
    tx_data[0] = (uint8_t)(max_threshold >> 8);        // Max threshold MSB
    tx_data[1] = (uint8_t)(max_threshold & 0xFF);      // Max threshold LSB
    tx_data[2] = (uint8_t)(min_threshold >> 8);        // Min threshold MSB
    tx_data[3] = (uint8_t)(min_threshold & 0xFF);      // Min threshold LSB
    
    // 使用软件I2C写入四个连续寄存器
    return Soft_I2C_Mem_Write(hltc2945->device_address, LTC2945_REG_MAX_SENSE_THRESHOLD_MSB, I2C_MEMADD_SIZE_8BIT, tx_data, 4);
}

/**
 * @brief 批量读取连续寄存器
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @param start_reg: 起始寄存器地址
 * @param data: 数据缓冲区
 * @param length: 要读取的字节数
 * @retval HAL status
 */
static HAL_StatusTypeDef LTC2945_BulkRead(LTC2945_Handle_t *hltc2945, uint8_t start_reg, uint8_t *data, uint8_t length)
{
    uint8_t i;
    
    // 发送起始条件和地址
    Soft_I2C_Start();
    Soft_I2C_SendByte(LTC2945_I2C_ADDR_WRITE);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 发送起始寄存器地址
    Soft_I2C_SendByte(start_reg);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 重复起始条件
    Soft_I2C_Start();
    Soft_I2C_SendByte(LTC2945_I2C_ADDR_READ);
    if(Soft_I2C_WaitAck())
    {
        Soft_I2C_Stop();
        return HAL_ERROR;
    }
    
    // 连续读取数据
    for(i = 0; i < length; i++)
    {
        data[i] = Soft_I2C_ReadByte(i < (length - 1) ? 1 : 0);
    }
    
    Soft_I2C_Stop();
    return HAL_OK;
}

/**
 * @brief Read sense voltage (current measurement)
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @param sense_voltage: Pointer to store 16-bit sense voltage value
 * @retval HAL status
 */
HAL_StatusTypeDef LTC2945_ReadSenseVoltage(LTC2945_Handle_t *hltc2945, uint16_t *sense_voltage)
{
    uint8_t buf[2];
    HAL_StatusTypeDef status;
    
    // 批量读取2个字节
    status = LTC2945_BulkRead(hltc2945, LTC2945_REG_SENSE_MSB, buf, 2);
    if (status != HAL_OK) {
        return status;
    }
    
    // 根据Linux驱动，12位数据存储在16位的高12位
    *sense_voltage = ((uint16_t)buf[0] << 4) | (buf[1] >> 4);
    return HAL_OK;
}

/**
 * @brief Read power measurement (24-bit value)
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @param power: Pointer to store 32-bit power value (only lower 24 bits used)
 * @retval HAL status
 */
HAL_StatusTypeDef LTC2945_ReadPower(LTC2945_Handle_t *hltc2945, uint32_t *power)
{
    uint8_t buf[3];
    HAL_StatusTypeDef status;
    
    // 批量读取3个字节
    status = LTC2945_BulkRead(hltc2945, LTC2945_REG_POWER_MSB2, buf, 3);
    if (status != HAL_OK) {
        return status;
    }
    
    // 24位功率数据
    *power = ((uint32_t)buf[0] << 16) | ((uint32_t)buf[1] << 8) | buf[2];
    return HAL_OK;
}

/**
 * @brief Read VIN voltage
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @param vin_voltage: Pointer to store 16-bit VIN voltage value
 * @retval HAL status
 */
HAL_StatusTypeDef LTC2945_ReadVinVoltage(LTC2945_Handle_t *hltc2945, uint16_t *vin_voltage)
{
    uint8_t buf[2];
    HAL_StatusTypeDef status;
    
    // 批量读取2个字节
    status = LTC2945_BulkRead(hltc2945, LTC2945_REG_VIN_MSB, buf, 2);
    if (status != HAL_OK) {
        return status;
    }
    
    // 12位电压数据存储在16位的高12位
    *vin_voltage = ((uint16_t)buf[0] << 4) | (buf[1] >> 4);
    return HAL_OK;
}

/**
 * @brief Read ADIN voltage
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @param adin_voltage: Pointer to store 16-bit ADIN voltage value
 * @retval HAL status
 */
HAL_StatusTypeDef LTC2945_ReadAdinVoltage(LTC2945_Handle_t *hltc2945, uint16_t *adin_voltage)
{
    uint8_t buf[2];
    HAL_StatusTypeDef status;
    
    // 批量读取2个字节
    status = LTC2945_BulkRead(hltc2945, LTC2945_REG_ADIN_MSB, buf, 2);
    if (status != HAL_OK) {
        return status;
    }
    
    // 12位电压数据存储在16位的高12位
    *adin_voltage = ((uint16_t)buf[0] << 4) | (buf[1] >> 4);
    return HAL_OK;
}

/**
 * @brief Read all measurement data from LTC2945
 * @param hltc2945: Pointer to LTC2945 handle structure
 * @param data: Pointer to LTC2945_Data_t structure to store all measurements
 * @retval HAL status
 */
HAL_StatusTypeDef LTC2945_ReadAllData(LTC2945_Handle_t *hltc2945, LTC2945_Data_t *data)
{
    HAL_StatusTypeDef status;
    
    status = LTC2945_ReadSenseVoltage(hltc2945, &data->sense_voltage);
    if (status != HAL_OK) return status;
    
    status = LTC2945_ReadPower(hltc2945, &data->power);
    if (status != HAL_OK) return status;
    
    status = LTC2945_ReadVinVoltage(hltc2945, &data->vin_voltage);
    if (status != HAL_OK) return status;
    
    status = LTC2945_ReadAdinVoltage(hltc2945, &data->adin_voltage);
    if (status != HAL_OK) return status;
    
    return HAL_OK;
}

/**
 * @brief Convert sense voltage raw value to current in Amperes
 * @param sense_value: Raw sense voltage value from registers (12-bit)
 * @param sense_resistor_ohms: Sense resistor value in Ohms
 * @retval Current in Amperes
 */
float LTC2945_ConvertSenseToAmps(uint16_t sense_value, float sense_resistor_ohms)
{
    // LTC2945 sense voltage LSB = 25µV/16 (12-bit data)
    // Full scale 81.9175mV for 12-bit (4095 counts)
    float sense_voltage = sense_value * 25e-6f;  // 25µV per LSB
    return sense_voltage / sense_resistor_ohms;  // I = V/R
}

/**
 * @brief Convert power raw value to power in Watts
 * @param power_value: Raw power value from registers (24-bit)
 * @param sense_resistor_ohms: Sense resistor value in Ohms
 * @retval Power in Watts
 */
float LTC2945_ConvertPowerToWatts(uint32_t power_value, float sense_resistor_ohms)
{
    // LTC2945 power LSB = 200µW for 1mΩ sense resistor
    float power_lsb = 200e-6f / sense_resistor_ohms;  // Scale for actual sense resistor
    return power_value * power_lsb;
}

/**
 * @brief Convert VIN raw value to voltage in Volts
 * @param vin_value: Raw VIN value from registers (12-bit)
 * @retval Voltage in Volts
 */
float LTC2945_ConvertVinToVolts(uint16_t vin_value)
{
    // LTC2945 VIN LSB = 25mV/16 for 12-bit data
    // Full scale 102.375V for 12-bit (4095 counts)
    return vin_value * 25e-3f;  // 25mV per LSB
}

/**
 * @brief Convert ADIN raw value to voltage in Volts
 * @param adin_value: Raw ADIN value from registers (12-bit)
 * @retval Voltage in Volts
 */
float LTC2945_ConvertAdinToVolts(uint16_t adin_value)
{
    // LTC2945 ADIN LSB = 500µV/16 for 12-bit data
    // Full scale 2.0475V for 12-bit (4095 counts)
    return adin_value * 500e-6f;  // 500µV per LSB
}
