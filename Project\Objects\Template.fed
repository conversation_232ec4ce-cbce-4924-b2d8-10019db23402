;#<FEEDBACK># ARM Linker, 5060960: Last Updated: Wed Jun 11 16:22:25 2025
;VERSION 0.2
;FILE adc.o
__asm___5_adc_c_ADC_Init____REV16 <= USED 0
__asm___5_adc_c_ADC_Init____REVSH <= USED 0
__asm___5_adc_c_ADC_Init____RRX <= USED 0
;FILE can.o
__asm___5_can_c_ef2b836b____REV16 <= USED 0
__asm___5_can_c_ef2b836b____REVSH <= USED 0
__asm___5_can_c_ef2b836b____RRX <= USED 0
;FILE candataprocess.o
SetProductInfo <= USED 0
__asm___16_CanDataProcess_c_0168e307____REV16 <= USED 0
__asm___16_CanDataProcess_c_0168e307____REVSH <= USED 0
__asm___16_CanDataProcess_c_0168e307____RRX <= USED 0
;FILE delay.o
__asm___7_delay_c_f6a9c549____REV16 <= USED 0
__asm___7_delay_c_f6a9c549____REVSH <= USED 0
__asm___7_delay_c_f6a9c549____RRX <= USED 0
;FILE fpga_ctrl.o
FPGA_SetCalAtten <= USED 0
FPGA_WaitResponse <= USED 0
__asm___11_fpga_ctrl_c_dfc46c4b____REV16 <= USED 0
__asm___11_fpga_ctrl_c_dfc46c4b____REVSH <= USED 0
__asm___11_fpga_ctrl_c_dfc46c4b____RRX <= USED 0
;FILE gpio.o
GpioInit <= USED 0
__asm___6_gpio_c_GpioInit____REV16 <= USED 0
__asm___6_gpio_c_GpioInit____REVSH <= USED 0
__asm___6_gpio_c_GpioInit____RRX <= USED 0
;FILE main.o
__asm___6_main_c_d0e7f69f____REV16 <= USED 0
__asm___6_main_c_d0e7f69f____REVSH <= USED 0
__asm___6_main_c_d0e7f69f____RRX <= USED 0
;FILE spi.o
SPI1_SendData <= USED 0
SPI3_SendData <= USED 0
__asm___5_spi_c_1b04b235____REV16 <= USED 0
__asm___5_spi_c_1b04b235____REVSH <= USED 0
__asm___5_spi_c_1b04b235____RRX <= USED 0
;FILE startup_stm32f107xc.o
;FILE stm32f1xx_hal.o
HAL_DBGMCU_DisableDBGSleepMode <= USED 0
HAL_DBGMCU_DisableDBGStandbyMode <= USED 0
HAL_DBGMCU_DisableDBGStopMode <= USED 0
HAL_DBGMCU_EnableDBGSleepMode <= USED 0
HAL_DBGMCU_EnableDBGStandbyMode <= USED 0
HAL_DBGMCU_EnableDBGStopMode <= USED 0
HAL_DeInit <= USED 0
HAL_Delay <= USED 0
HAL_GetDEVID <= USED 0
HAL_GetHalVersion <= USED 0
HAL_GetREVID <= USED 0
HAL_GetTickFreq <= USED 0
HAL_GetTickPrio <= USED 0
HAL_GetUIDw0 <= USED 0
HAL_GetUIDw1 <= USED 0
HAL_GetUIDw2 <= USED 0
HAL_MspDeInit <= USED 0
HAL_ResumeTick <= USED 0
HAL_SetTickFreq <= USED 0
HAL_SuspendTick <= USED 0
__asm___15_stm32f1xx_hal_c_3da258af____REV16 <= USED 0
__asm___15_stm32f1xx_hal_c_3da258af____REVSH <= USED 0
__asm___15_stm32f1xx_hal_c_3da258af____RRX <= USED 0
;FILE stm32f1xx_hal_adc.o
ADC_DMAConvCplt <= USED 0
ADC_DMAError <= USED 0
ADC_DMAHalfConvCplt <= USED 0
HAL_ADC_AnalogWDGConfig <= USED 0
HAL_ADC_ConvCpltCallback <= USED 0
HAL_ADC_ConvHalfCpltCallback <= USED 0
HAL_ADC_DeInit <= USED 0
HAL_ADC_ErrorCallback <= USED 0
HAL_ADC_GetError <= USED 0
HAL_ADC_GetState <= USED 0
HAL_ADC_IRQHandler <= USED 0
HAL_ADC_LevelOutOfWindowCallback <= USED 0
HAL_ADC_MspDeInit <= USED 0
HAL_ADC_PollForEvent <= USED 0
HAL_ADC_Start_DMA <= USED 0
HAL_ADC_Start_IT <= USED 0
HAL_ADC_Stop <= USED 0
HAL_ADC_Stop_DMA <= USED 0
HAL_ADC_Stop_IT <= USED 0
__asm___19_stm32f1xx_hal_adc_c_52ef8f50____REV16 <= USED 0
__asm___19_stm32f1xx_hal_adc_c_52ef8f50____REVSH <= USED 0
__asm___19_stm32f1xx_hal_adc_c_52ef8f50____RRX <= USED 0
;FILE stm32f1xx_hal_adc_ex.o
HAL_ADCEx_InjectedConfigChannel <= USED 0
HAL_ADCEx_InjectedConvCpltCallback <= USED 0
HAL_ADCEx_InjectedGetValue <= USED 0
HAL_ADCEx_InjectedPollForConversion <= USED 0
HAL_ADCEx_InjectedStart <= USED 0
HAL_ADCEx_InjectedStart_IT <= USED 0
HAL_ADCEx_InjectedStop <= USED 0
HAL_ADCEx_InjectedStop_IT <= USED 0
HAL_ADCEx_MultiModeConfigChannel <= USED 0
HAL_ADCEx_MultiModeGetValue <= USED 0
HAL_ADCEx_MultiModeStart_DMA <= USED 0
HAL_ADCEx_MultiModeStop_DMA <= USED 0
__asm___22_stm32f1xx_hal_adc_ex_c_3613cd7f____REV16 <= USED 0
__asm___22_stm32f1xx_hal_adc_ex_c_3613cd7f____REVSH <= USED 0
__asm___22_stm32f1xx_hal_adc_ex_c_3613cd7f____RRX <= USED 0
;FILE stm32f1xx_hal_can.o
HAL_CAN_AbortTxRequest <= USED 0
HAL_CAN_DeInit <= USED 0
HAL_CAN_DeactivateNotification <= USED 0
HAL_CAN_GetError <= USED 0
HAL_CAN_GetRxFifoFillLevel <= USED 0
HAL_CAN_GetState <= USED 0
HAL_CAN_GetTxMailboxesFreeLevel <= USED 0
HAL_CAN_GetTxTimestamp <= USED 0
HAL_CAN_IsSleepActive <= USED 0
HAL_CAN_IsTxMessagePending <= USED 0
HAL_CAN_MspDeInit <= USED 0
HAL_CAN_RequestSleep <= USED 0
HAL_CAN_ResetError <= USED 0
HAL_CAN_Stop <= USED 0
HAL_CAN_WakeUp <= USED 0
__asm___19_stm32f1xx_hal_can_c_fe6cb454____REV16 <= USED 0
__asm___19_stm32f1xx_hal_can_c_fe6cb454____REVSH <= USED 0
__asm___19_stm32f1xx_hal_can_c_fe6cb454____RRX <= USED 0
;FILE stm32f1xx_hal_cortex.o
HAL_NVIC_ClearPendingIRQ <= USED 0
HAL_NVIC_DisableIRQ <= USED 0
HAL_NVIC_GetActive <= USED 0
HAL_NVIC_GetPendingIRQ <= USED 0
HAL_NVIC_GetPriority <= USED 0
HAL_NVIC_GetPriorityGrouping <= USED 0
HAL_NVIC_SetPendingIRQ <= USED 0
HAL_NVIC_SystemReset <= USED 0
HAL_SYSTICK_Callback <= USED 0
HAL_SYSTICK_IRQHandler <= USED 0
__asm___22_stm32f1xx_hal_cortex_c_2992dbc0____REV16 <= USED 0
__asm___22_stm32f1xx_hal_cortex_c_2992dbc0____REVSH <= USED 0
__asm___22_stm32f1xx_hal_cortex_c_2992dbc0____RRX <= USED 0
;FILE stm32f1xx_hal_crc.o
__asm___19_stm32f1xx_hal_crc_c____REV16 <= USED 0
__asm___19_stm32f1xx_hal_crc_c____REVSH <= USED 0
__asm___19_stm32f1xx_hal_crc_c____RRX <= USED 0
;FILE stm32f1xx_hal_dac.o
__asm___19_stm32f1xx_hal_dac_c____REV16 <= USED 0
__asm___19_stm32f1xx_hal_dac_c____REVSH <= USED 0
__asm___19_stm32f1xx_hal_dac_c____RRX <= USED 0
;FILE stm32f1xx_hal_dac_ex.o
__asm___22_stm32f1xx_hal_dac_ex_c____REV16 <= USED 0
__asm___22_stm32f1xx_hal_dac_ex_c____REVSH <= USED 0
__asm___22_stm32f1xx_hal_dac_ex_c____RRX <= USED 0
;FILE stm32f1xx_hal_dma.o
HAL_DMA_DeInit <= USED 0
HAL_DMA_GetError <= USED 0
HAL_DMA_GetState <= USED 0
HAL_DMA_IRQHandler <= USED 0
HAL_DMA_Init <= USED 0
HAL_DMA_PollForTransfer <= USED 0
HAL_DMA_RegisterCallback <= USED 0
HAL_DMA_Start <= USED 0
HAL_DMA_UnRegisterCallback <= USED 0
__asm___19_stm32f1xx_hal_dma_c_c25f65ec____REV16 <= USED 0
__asm___19_stm32f1xx_hal_dma_c_c25f65ec____REVSH <= USED 0
__asm___19_stm32f1xx_hal_dma_c_c25f65ec____RRX <= USED 0
;FILE stm32f1xx_hal_exti.o
HAL_EXTI_ClearConfigLine <= USED 0
HAL_EXTI_ClearPending <= USED 0
HAL_EXTI_GenerateSWI <= USED 0
HAL_EXTI_GetConfigLine <= USED 0
HAL_EXTI_GetHandle <= USED 0
HAL_EXTI_GetPending <= USED 0
HAL_EXTI_IRQHandler <= USED 0
HAL_EXTI_RegisterCallback <= USED 0
HAL_EXTI_SetConfigLine <= USED 0
__asm___20_stm32f1xx_hal_exti_c_ad9bfa1e____REV16 <= USED 0
__asm___20_stm32f1xx_hal_exti_c_ad9bfa1e____REVSH <= USED 0
__asm___20_stm32f1xx_hal_exti_c_ad9bfa1e____RRX <= USED 0
;FILE stm32f1xx_hal_flash.o
FLASH_WaitForLastOperation <= USED 0
HAL_FLASH_EndOfOperationCallback <= USED 0
HAL_FLASH_GetError <= USED 0
HAL_FLASH_IRQHandler <= USED 0
HAL_FLASH_Lock <= USED 0
HAL_FLASH_OB_Launch <= USED 0
HAL_FLASH_OB_Lock <= USED 0
HAL_FLASH_OB_Unlock <= USED 0
HAL_FLASH_OperationErrorCallback <= USED 0
HAL_FLASH_Program <= USED 0
HAL_FLASH_Program_IT <= USED 0
HAL_FLASH_Unlock <= USED 0
__asm___21_stm32f1xx_hal_flash_c_48aa8f3e____REV16 <= USED 0
__asm___21_stm32f1xx_hal_flash_c_48aa8f3e____REVSH <= USED 0
__asm___21_stm32f1xx_hal_flash_c_48aa8f3e____RRX <= USED 0
;FILE stm32f1xx_hal_flash_ex.o
FLASH_PageErase <= USED 0
HAL_FLASHEx_Erase <= USED 0
HAL_FLASHEx_Erase_IT <= USED 0
HAL_FLASHEx_OBErase <= USED 0
HAL_FLASHEx_OBGetConfig <= USED 0
HAL_FLASHEx_OBGetUserData <= USED 0
HAL_FLASHEx_OBProgram <= USED 0
__asm___24_stm32f1xx_hal_flash_ex_c_6648b60e____REV16 <= USED 0
__asm___24_stm32f1xx_hal_flash_ex_c_6648b60e____REVSH <= USED 0
__asm___24_stm32f1xx_hal_flash_ex_c_6648b60e____RRX <= USED 0
;FILE stm32f1xx_hal_gpio.o
HAL_GPIO_DeInit <= USED 0
HAL_GPIO_EXTI_Callback <= USED 0
HAL_GPIO_EXTI_IRQHandler <= USED 0
HAL_GPIO_LockPin <= USED 0
HAL_GPIO_ReadPin <= USED 0
HAL_GPIO_TogglePin <= USED 0
HAL_GPIO_WritePin <= USED 0
__asm___20_stm32f1xx_hal_gpio_c_ea787061____REV16 <= USED 0
__asm___20_stm32f1xx_hal_gpio_c_ea787061____REVSH <= USED 0
__asm___20_stm32f1xx_hal_gpio_c_ea787061____RRX <= USED 0
;FILE stm32f1xx_hal_gpio_ex.o
HAL_GPIOEx_ConfigEventout <= USED 0
HAL_GPIOEx_DisableEventout <= USED 0
HAL_GPIOEx_EnableEventout <= USED 0
__asm___23_stm32f1xx_hal_gpio_ex_c_61b0f410____REV16 <= USED 0
__asm___23_stm32f1xx_hal_gpio_ex_c_61b0f410____REVSH <= USED 0
__asm___23_stm32f1xx_hal_gpio_ex_c_61b0f410____RRX <= USED 0
;FILE stm32f1xx_hal_pwr.o
HAL_PWR_ConfigPVD <= USED 0
HAL_PWR_DeInit <= USED 0
HAL_PWR_DisableBkUpAccess <= USED 0
HAL_PWR_DisablePVD <= USED 0
HAL_PWR_DisableSEVOnPend <= USED 0
HAL_PWR_DisableSleepOnExit <= USED 0
HAL_PWR_DisableWakeUpPin <= USED 0
HAL_PWR_EnableBkUpAccess <= USED 0
HAL_PWR_EnablePVD <= USED 0
HAL_PWR_EnableSEVOnPend <= USED 0
HAL_PWR_EnableSleepOnExit <= USED 0
HAL_PWR_EnableWakeUpPin <= USED 0
HAL_PWR_EnterSLEEPMode <= USED 0
HAL_PWR_EnterSTANDBYMode <= USED 0
HAL_PWR_EnterSTOPMode <= USED 0
HAL_PWR_PVDCallback <= USED 0
HAL_PWR_PVD_IRQHandler <= USED 0
__asm___19_stm32f1xx_hal_pwr_c_f2cfe8be____REV16 <= USED 0
__asm___19_stm32f1xx_hal_pwr_c_f2cfe8be____REVSH <= USED 0
__asm___19_stm32f1xx_hal_pwr_c_f2cfe8be____RRX <= USED 0
;FILE stm32f1xx_hal_rcc.o
HAL_RCC_CSSCallback <= USED 0
HAL_RCC_DeInit <= USED 0
HAL_RCC_DisableCSS <= USED 0
HAL_RCC_EnableCSS <= USED 0
HAL_RCC_GetClockConfig <= USED 0
HAL_RCC_GetOscConfig <= USED 0
HAL_RCC_GetPCLK1Freq <= USED 0
HAL_RCC_MCOConfig <= USED 0
HAL_RCC_NMI_IRQHandler <= USED 0
__asm___19_stm32f1xx_hal_rcc_c_b7071a4b____REV16 <= USED 0
__asm___19_stm32f1xx_hal_rcc_c_b7071a4b____REVSH <= USED 0
__asm___19_stm32f1xx_hal_rcc_c_b7071a4b____RRX <= USED 0
;FILE stm32f1xx_hal_rcc_ex.o
HAL_RCCEx_DisablePLL2 <= USED 0
HAL_RCCEx_DisablePLLI2S <= USED 0
HAL_RCCEx_EnablePLL2 <= USED 0
HAL_RCCEx_EnablePLLI2S <= USED 0
HAL_RCCEx_GetPeriphCLKConfig <= USED 0
__asm___22_stm32f1xx_hal_rcc_ex_c_bed13b44____REV16 <= USED 0
__asm___22_stm32f1xx_hal_rcc_ex_c_bed13b44____REVSH <= USED 0
__asm___22_stm32f1xx_hal_rcc_ex_c_bed13b44____RRX <= USED 0
;FILE stm32f1xx_hal_spi.o
HAL_SPI_Abort <= USED 0
HAL_SPI_AbortCpltCallback <= USED 0
HAL_SPI_Abort_IT <= USED 0
HAL_SPI_DMAPause <= USED 0
HAL_SPI_DMAResume <= USED 0
HAL_SPI_DMAStop <= USED 0
HAL_SPI_DeInit <= USED 0
HAL_SPI_ErrorCallback <= USED 0
HAL_SPI_GetError <= USED 0
HAL_SPI_GetState <= USED 0
HAL_SPI_IRQHandler <= USED 0
HAL_SPI_MspDeInit <= USED 0
HAL_SPI_Receive <= USED 0
HAL_SPI_Receive_DMA <= USED 0
HAL_SPI_Receive_IT <= USED 0
HAL_SPI_RxCpltCallback <= USED 0
HAL_SPI_RxHalfCpltCallback <= USED 0
HAL_SPI_Transmit <= USED 0
HAL_SPI_TransmitReceive_DMA <= USED 0
HAL_SPI_TransmitReceive_IT <= USED 0
HAL_SPI_Transmit_DMA <= USED 0
HAL_SPI_Transmit_IT <= USED 0
HAL_SPI_TxCpltCallback <= USED 0
HAL_SPI_TxHalfCpltCallback <= USED 0
HAL_SPI_TxRxCpltCallback <= USED 0
HAL_SPI_TxRxHalfCpltCallback <= USED 0
__asm___19_stm32f1xx_hal_spi_c_5e7b22bd____REV16 <= USED 0
__asm___19_stm32f1xx_hal_spi_c_5e7b22bd____REVSH <= USED 0
__asm___19_stm32f1xx_hal_spi_c_5e7b22bd____RRX <= USED 0
;FILE stm32f1xx_hal_tim.o
HAL_TIM_Base_DeInit <= USED 0
HAL_TIM_Base_GetState <= USED 0
HAL_TIM_Base_Init <= USED 0
HAL_TIM_Base_MspDeInit <= USED 0
HAL_TIM_Base_MspInit <= USED 0
HAL_TIM_Base_Start <= USED 0
HAL_TIM_Base_Start_DMA <= USED 0
HAL_TIM_Base_Start_IT <= USED 0
HAL_TIM_Base_Stop <= USED 0
HAL_TIM_Base_Stop_DMA <= USED 0
HAL_TIM_Base_Stop_IT <= USED 0
HAL_TIM_ConfigClockSource <= USED 0
HAL_TIM_ConfigOCrefClear <= USED 0
HAL_TIM_ConfigTI1Input <= USED 0
HAL_TIM_DMABurstState <= USED 0
HAL_TIM_DMABurst_MultiReadStart <= USED 0
HAL_TIM_DMABurst_MultiWriteStart <= USED 0
HAL_TIM_DMABurst_ReadStart <= USED 0
HAL_TIM_DMABurst_ReadStop <= USED 0
HAL_TIM_DMABurst_WriteStart <= USED 0
HAL_TIM_DMABurst_WriteStop <= USED 0
HAL_TIM_Encoder_DeInit <= USED 0
HAL_TIM_Encoder_GetState <= USED 0
HAL_TIM_Encoder_Init <= USED 0
HAL_TIM_Encoder_MspDeInit <= USED 0
HAL_TIM_Encoder_MspInit <= USED 0
HAL_TIM_Encoder_Start <= USED 0
HAL_TIM_Encoder_Start_DMA <= USED 0
HAL_TIM_Encoder_Start_IT <= USED 0
HAL_TIM_Encoder_Stop <= USED 0
HAL_TIM_Encoder_Stop_DMA <= USED 0
HAL_TIM_Encoder_Stop_IT <= USED 0
HAL_TIM_ErrorCallback <= USED 0
HAL_TIM_GenerateEvent <= USED 0
HAL_TIM_GetActiveChannel <= USED 0
HAL_TIM_GetChannelState <= USED 0
HAL_TIM_IC_CaptureCallback <= USED 0
HAL_TIM_IC_CaptureHalfCpltCallback <= USED 0
HAL_TIM_IC_ConfigChannel <= USED 0
HAL_TIM_IC_DeInit <= USED 0
HAL_TIM_IC_GetState <= USED 0
HAL_TIM_IC_Init <= USED 0
HAL_TIM_IC_MspDeInit <= USED 0
HAL_TIM_IC_MspInit <= USED 0
HAL_TIM_IC_Start <= USED 0
HAL_TIM_IC_Start_DMA <= USED 0
HAL_TIM_IC_Start_IT <= USED 0
HAL_TIM_IC_Stop <= USED 0
HAL_TIM_IC_Stop_DMA <= USED 0
HAL_TIM_IC_Stop_IT <= USED 0
HAL_TIM_IRQHandler <= USED 0
HAL_TIM_OC_ConfigChannel <= USED 0
HAL_TIM_OC_DeInit <= USED 0
HAL_TIM_OC_DelayElapsedCallback <= USED 0
HAL_TIM_OC_GetState <= USED 0
HAL_TIM_OC_Init <= USED 0
HAL_TIM_OC_MspDeInit <= USED 0
HAL_TIM_OC_MspInit <= USED 0
HAL_TIM_OC_Start <= USED 0
HAL_TIM_OC_Start_DMA <= USED 0
HAL_TIM_OC_Start_IT <= USED 0
HAL_TIM_OC_Stop <= USED 0
HAL_TIM_OC_Stop_DMA <= USED 0
HAL_TIM_OC_Stop_IT <= USED 0
HAL_TIM_OnePulse_ConfigChannel <= USED 0
HAL_TIM_OnePulse_DeInit <= USED 0
HAL_TIM_OnePulse_GetState <= USED 0
HAL_TIM_OnePulse_Init <= USED 0
HAL_TIM_OnePulse_MspDeInit <= USED 0
HAL_TIM_OnePulse_MspInit <= USED 0
HAL_TIM_OnePulse_Start <= USED 0
HAL_TIM_OnePulse_Start_IT <= USED 0
HAL_TIM_OnePulse_Stop <= USED 0
HAL_TIM_OnePulse_Stop_IT <= USED 0
HAL_TIM_PWM_ConfigChannel <= USED 0
HAL_TIM_PWM_DeInit <= USED 0
HAL_TIM_PWM_GetState <= USED 0
HAL_TIM_PWM_Init <= USED 0
HAL_TIM_PWM_MspDeInit <= USED 0
HAL_TIM_PWM_MspInit <= USED 0
HAL_TIM_PWM_PulseFinishedCallback <= USED 0
HAL_TIM_PWM_PulseFinishedHalfCpltCallback <= USED 0
HAL_TIM_PWM_Start <= USED 0
HAL_TIM_PWM_Start_DMA <= USED 0
HAL_TIM_PWM_Start_IT <= USED 0
HAL_TIM_PWM_Stop <= USED 0
HAL_TIM_PWM_Stop_DMA <= USED 0
HAL_TIM_PWM_Stop_IT <= USED 0
HAL_TIM_PeriodElapsedCallback <= USED 0
HAL_TIM_PeriodElapsedHalfCpltCallback <= USED 0
HAL_TIM_ReadCapturedValue <= USED 0
HAL_TIM_SlaveConfigSynchro <= USED 0
HAL_TIM_SlaveConfigSynchro_IT <= USED 0
HAL_TIM_TriggerCallback <= USED 0
HAL_TIM_TriggerHalfCpltCallback <= USED 0
TIM_Base_SetConfig <= USED 0
TIM_CCxChannelCmd <= USED 0
TIM_DMACaptureCplt <= USED 0
TIM_DMACaptureHalfCplt <= USED 0
TIM_DMADelayPulseHalfCplt <= USED 0
TIM_DMAError <= USED 0
TIM_ETR_SetConfig <= USED 0
TIM_OC2_SetConfig <= USED 0
TIM_TI1_SetConfig <= USED 0
__asm___19_stm32f1xx_hal_tim_c_a9d95b52____REV16 <= USED 0
__asm___19_stm32f1xx_hal_tim_c_a9d95b52____REVSH <= USED 0
__asm___19_stm32f1xx_hal_tim_c_a9d95b52____RRX <= USED 0
;FILE stm32f1xx_hal_tim_ex.o
HAL_TIMEx_BreakCallback <= USED 0
HAL_TIMEx_CommutCallback <= USED 0
HAL_TIMEx_CommutHalfCpltCallback <= USED 0
HAL_TIMEx_ConfigBreakDeadTime <= USED 0
HAL_TIMEx_ConfigCommutEvent <= USED 0
HAL_TIMEx_ConfigCommutEvent_DMA <= USED 0
HAL_TIMEx_ConfigCommutEvent_IT <= USED 0
HAL_TIMEx_GetChannelNState <= USED 0
HAL_TIMEx_HallSensor_DeInit <= USED 0
HAL_TIMEx_HallSensor_GetState <= USED 0
HAL_TIMEx_HallSensor_Init <= USED 0
HAL_TIMEx_HallSensor_MspDeInit <= USED 0
HAL_TIMEx_HallSensor_MspInit <= USED 0
HAL_TIMEx_HallSensor_Start <= USED 0
HAL_TIMEx_HallSensor_Start_DMA <= USED 0
HAL_TIMEx_HallSensor_Start_IT <= USED 0
HAL_TIMEx_HallSensor_Stop <= USED 0
HAL_TIMEx_HallSensor_Stop_DMA <= USED 0
HAL_TIMEx_HallSensor_Stop_IT <= USED 0
HAL_TIMEx_MasterConfigSynchronization <= USED 0
HAL_TIMEx_OCN_Start <= USED 0
HAL_TIMEx_OCN_Start_DMA <= USED 0
HAL_TIMEx_OCN_Start_IT <= USED 0
HAL_TIMEx_OCN_Stop <= USED 0
HAL_TIMEx_OCN_Stop_DMA <= USED 0
HAL_TIMEx_OCN_Stop_IT <= USED 0
HAL_TIMEx_OnePulseN_Start <= USED 0
HAL_TIMEx_OnePulseN_Start_IT <= USED 0
HAL_TIMEx_OnePulseN_Stop <= USED 0
HAL_TIMEx_OnePulseN_Stop_IT <= USED 0
HAL_TIMEx_PWMN_Start <= USED 0
HAL_TIMEx_PWMN_Start_DMA <= USED 0
HAL_TIMEx_PWMN_Start_IT <= USED 0
HAL_TIMEx_PWMN_Stop <= USED 0
HAL_TIMEx_PWMN_Stop_DMA <= USED 0
HAL_TIMEx_PWMN_Stop_IT <= USED 0
HAL_TIMEx_RemapConfig <= USED 0
TIMEx_DMACommutationCplt <= USED 0
TIMEx_DMACommutationHalfCplt <= USED 0
__asm___22_stm32f1xx_hal_tim_ex_c_e8ef3920____REV16 <= USED 0
__asm___22_stm32f1xx_hal_tim_ex_c_e8ef3920____REVSH <= USED 0
__asm___22_stm32f1xx_hal_tim_ex_c_e8ef3920____RRX <= USED 0
;FILE stm32f1xx_hal_uart.o
HAL_HalfDuplex_EnableReceiver <= USED 0
HAL_HalfDuplex_EnableTransmitter <= USED 0
HAL_HalfDuplex_Init <= USED 0
HAL_LIN_Init <= USED 0
HAL_LIN_SendBreak <= USED 0
HAL_MultiProcessor_EnterMuteMode <= USED 0
HAL_MultiProcessor_ExitMuteMode <= USED 0
HAL_MultiProcessor_Init <= USED 0
HAL_UARTEx_ReceiveToIdle <= USED 0
HAL_UARTEx_ReceiveToIdle_DMA <= USED 0
HAL_UARTEx_ReceiveToIdle_IT <= USED 0
HAL_UART_Abort <= USED 0
HAL_UART_AbortCpltCallback <= USED 0
HAL_UART_AbortReceive <= USED 0
HAL_UART_AbortReceiveCpltCallback <= USED 0
HAL_UART_AbortReceive_IT <= USED 0
HAL_UART_AbortTransmit <= USED 0
HAL_UART_AbortTransmitCpltCallback <= USED 0
HAL_UART_AbortTransmit_IT <= USED 0
HAL_UART_Abort_IT <= USED 0
HAL_UART_DMAPause <= USED 0
HAL_UART_DMAResume <= USED 0
HAL_UART_DeInit <= USED 0
HAL_UART_GetError <= USED 0
HAL_UART_GetState <= USED 0
HAL_UART_Init <= USED 0
HAL_UART_MspDeInit <= USED 0
HAL_UART_Receive <= USED 0
HAL_UART_Receive_IT <= USED 0
HAL_UART_Transmit <= USED 0
HAL_UART_Transmit_DMA <= USED 0
HAL_UART_Transmit_IT <= USED 0
HAL_UART_TxHalfCpltCallback <= USED 0
UART_Start_Receive_IT <= USED 0
__asm___20_stm32f1xx_hal_uart_c_d497114f____REV16 <= USED 0
__asm___20_stm32f1xx_hal_uart_c_d497114f____REVSH <= USED 0
__asm___20_stm32f1xx_hal_uart_c_d497114f____RRX <= USED 0
;FILE stm32f1xx_hal_usart.o
HAL_USART_Abort <= USED 0
HAL_USART_AbortCpltCallback <= USED 0
HAL_USART_Abort_IT <= USED 0
HAL_USART_DMAPause <= USED 0
HAL_USART_DMAResume <= USED 0
HAL_USART_DMAStop <= USED 0
HAL_USART_DeInit <= USED 0
HAL_USART_ErrorCallback <= USED 0
HAL_USART_GetError <= USED 0
HAL_USART_GetState <= USED 0
HAL_USART_IRQHandler <= USED 0
HAL_USART_Init <= USED 0
HAL_USART_MspDeInit <= USED 0
HAL_USART_MspInit <= USED 0
HAL_USART_Receive <= USED 0
HAL_USART_Receive_DMA <= USED 0
HAL_USART_Receive_IT <= USED 0
HAL_USART_RxCpltCallback <= USED 0
HAL_USART_RxHalfCpltCallback <= USED 0
HAL_USART_Transmit <= USED 0
HAL_USART_TransmitReceive <= USED 0
HAL_USART_TransmitReceive_DMA <= USED 0
HAL_USART_TransmitReceive_IT <= USED 0
HAL_USART_Transmit_DMA <= USED 0
HAL_USART_Transmit_IT <= USED 0
HAL_USART_TxCpltCallback <= USED 0
HAL_USART_TxHalfCpltCallback <= USED 0
HAL_USART_TxRxCpltCallback <= USED 0
__asm___21_stm32f1xx_hal_usart_c_32ae8742____REV16 <= USED 0
__asm___21_stm32f1xx_hal_usart_c_32ae8742____REVSH <= USED 0
__asm___21_stm32f1xx_hal_usart_c_32ae8742____RRX <= USED 0
;FILE stm32f1xx_it.o
__asm___14_stm32f1xx_it_c_bb8ca80c____REV16 <= USED 0
__asm___14_stm32f1xx_it_c_bb8ca80c____REVSH <= USED 0
__asm___14_stm32f1xx_it_c_bb8ca80c____RRX <= USED 0
;FILE stm32flash.o
STMFLASH_Write <= USED 0
__asm___12_STM32Flash_c_b5a188e1____REV16 <= USED 0
__asm___12_STM32Flash_c_b5a188e1____REVSH <= USED 0
__asm___12_STM32Flash_c_b5a188e1____RRX <= USED 0
;FILE sys.o
__asm___5_sys_c_4557ba80____REV16 <= USED 0
__asm___5_sys_c_4557ba80____REVSH <= USED 0
__asm___5_sys_c_4557ba80____RRX <= USED 0
;FILE system_stm32f1xx.o
SystemCoreClockUpdate <= USED 0
__asm___18_system_stm32f1xx_c_5d646a67____REV16 <= USED 0
__asm___18_system_stm32f1xx_c_5d646a67____REVSH <= USED 0
__asm___18_system_stm32f1xx_c_5d646a67____RRX <= USED 0
;FILE usart.o
HAL_UART_MspInit <= USED 0
Usart1_Init <= USED 0
__asm___7_usart_c_7cc17ae7____REV16 <= USED 0
__asm___7_usart_c_7cc17ae7____REVSH <= USED 0
__asm___7_usart_c_7cc17ae7____RRX <= USED 0
fgetc <= USED 0
fputc <= USED 0
