/**
  ******************************************************************************
  * @file       : STM32Flash.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : STM32内部Flash控制
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */
  
/* 头文件包含 ----------------------------------------------------------------*/
#include "STM32Flash.h"
#include "sys.h"


/**
  * @brief  指定地址写入字
  * @param  WriteAddr -- 写入的地址   WriteData -- 写入的数据 WriteLength -- 写入数据长度
  * @retval 无
  */
void STMFLASH_Write(uint32_t WriteAddr, uint32_t WriteData[], uint8_t WriteLength)	
{
	uint32_t SECTORError = 0, StartAddr;
	uint8_t i;
	FLASH_EraseInitTypeDef EraseInitStruct; 
	
	// 解锁
	HAL_FLASH_Unlock();	
	
	// 擦除扇区
	EraseInitStruct.TypeErase     = FLASH_TYPEERASE_PAGES;
	EraseInitStruct.NbPages       = 0x01;
	EraseInitStruct.PageAddress   = WriteAddr + STM32_FLASH_BASE;
	if (HAL_FLASHEx_Erase(&EraseInitStruct, &SECTORError) != HAL_OK)
	{
		Error_Handler(__FILE__, __LINE__);
	}
	
	// 数据写入
	StartAddr = WriteAddr + STM32_FLASH_BASE;
	for (i = 0; i < WriteLength; i++)
	{
		if (HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, StartAddr, WriteData[i]) == HAL_OK)
		{
			StartAddr += 4;
		}
		else
		{
			Error_Handler(__FILE__, __LINE__);
		}
	}

	// 上锁
	HAL_FLASH_Lock();	
}

/**
  * @brief  读取指定地址的数据(32位数据)
  * @param  faddr -- 读地址(此地址必须为2的倍数)
  * @retval 对应数据
  */
void Stm32Flash_Read(uint32_t ReadAddr, uint32_t *pBuffer, uint8_t ReadLength)
{
	uint8_t i;
	uint32_t Addr = STM32_FLASH_BASE + ReadAddr;
	
	for (i = 0; i < ReadLength; i++)
	{
		pBuffer[i] = *(__IO uint32_t*)Addr;
		Addr += 4;
	}
}

