/**
  ******************************************************************************
  * @file       : CanDataProcess.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : CAN总线数据处理实现
  ******************************************************************************
  * @attention
  *
  * 根据通信协议文档实现CAN数据解析和响应
  *
  ******************************************************************************
  */
  
/* 头文件包含 ----------------------------------------------------------------*/
#include "CanDataProcess.h"
#include "STM32Flash.h"
#include "delay.h"
#include "can.h"
#include "spi.h"
#include "adc.h"
#include "fpga_ctrl.h"
#include <string.h>

/* 宏定义 --------------------------------------------------------------------*/
#define MSG_CATEGORY_F0         0xF0    // 消息类别
#define MSG_ID_PRODUCT_QUERY    0x00    // 产品信息查询
#define MSG_ID_BIT_QUERY        0x01    // BIT查询
#define MSG_ID_PARAM_QUERY      0x02    // 通道参数查询
#define MSG_ID_PARAM_SET        0x04    // 通道参数设置

// 通道编号定义
#define CHANNEL_ALL             0x00    // 全通道
#define CHANNEL_LOW1            0x01    // 低段一本振1通道
#define CHANNEL_LOW2            0x02    // 低段一本振2通道
#define CHANNEL_LOW_SWITCH      0x03    // 低段一本振开关/功分切换
#define CHANNEL_HIGH1_1         0x04    // 高段一本振1通道
#define CHANNEL_HIGH2_1         0x05    // 高段二本振1通道
#define CHANNEL_HIGH1_2         0x06    // 高段一本振2通道
#define CHANNEL_HIGH2_2         0x07    // 高段二本振2通道
#define CHANNEL_HIGH1_3         0x08    // 高段一本振3通道
#define CHANNEL_HIGH2_3         0x09    // 高段二本振3通道

// 工作模式定义
#define MODE_SWITCH             0x00    // 开关工作模式
#define MODE_POWER_DIVIDER      0x01    // 功分工作模式
#define MODE_LOW_POWER          0x02    // 低功耗模式

/* 变量声明 -------------------------------------------------------------------*/
extern CAN_IdTypeDef CanIdConfig;	// CAN发送ID 

/* 全局变量定义 ---------------------------------------------------------------*/
ModuleParams_t moduleParams;              // 模块参数
ProductInfo_t productInfo;                // 产品信息
BitInfo_t bitInfo;                        // BIT信息

// 用于替代实际下发和上报的变量
uint8_t canTxBuffer[256];                 // CAN发送缓冲区
uint16_t canTxLength = 0;                 // CAN发送长度
uint8_t fpgaCommandBuffer[256];           // FPGA命令缓冲区
uint8_t fpgaCommandLength = 0;            // FPGA命令长度

/* 私有函数声明 ---------------------------------------------------------------*/
static void ProcessProductInfoQuery(uint8_t MarkAddr);
static void ProcessBitQuery(uint8_t MarkAddr);
static void ProcessParamQuery(uint8_t channelNum, uint8_t MarkAddr);
static void ProcessParamSet(uint8_t CanRxData[], uint8_t MarkAddr);
static void SendCanResponse(uint8_t MarkAddr);
static void SetProductInfo(void);

/**
  * @brief  初始化模块参数
  * @param  无
  * @retval 无
  */
void InitModuleParams(void)
{
    uint32_t productInfoData[3];
    
    // 从Flash读取产品信息
    Stm32Flash_Read(STORE_ADDR, productInfoData, 3);
    
    // 检查Flash是否为空（全F表示未写入）
    if (productInfoData[0] != 0xFFFFFFFF) {
        // Flash中有有效数据，解析产品信息
        productInfo.manufacturerCode = 0x00;    // 厂家代号固定为十所
        productInfo.productYear = (productInfoData[0] >> 16) & 0xFFFF;
        productInfo.productMonth = (productInfoData[0] >> 8) & 0xFF;
        productInfo.productDay = productInfoData[0] & 0xFF;
        productInfo.serialYear = (productInfoData[1] >> 16) & 0xFFFF;
        productInfo.serialBatch = (productInfoData[1] >> 8) & 0xFF;
        productInfo.serialNum = ((productInfoData[1] & 0xFF) << 8) | ((productInfoData[2] >> 8) & 0xFF);
    } else {
        // Flash为空，使用默认值
        productInfo.manufacturerCode = 0x00;    // 十所
        productInfo.productYear = 0x2025;       // 2025年 BCD
        productInfo.productMonth = 0x01;        // 1月 BCD
        productInfo.productDay = 0x01;          // 1日 BCD
        productInfo.serialYear = 0x2025;        // 2025年 BCD
        productInfo.serialBatch = 0x01;         // 批次01 BCD
        productInfo.serialNum = 0x0001;         // 序号001 BCD
    }
    
    // 初始化低段一本振参数
    moduleParams.lowChannel1.frequency = 1000000;    // 默认1MHz
    moduleParams.lowChannel1.workMode = MODE_POWER_DIVIDER;
    moduleParams.lowChannel2.frequency = 2000000;    // 默认2MHz
    moduleParams.lowChannel2.workMode = MODE_POWER_DIVIDER;
    moduleParams.lowSwitchMode = MODE_POWER_DIVIDER;
    
    // 初始化高段本振参数
    moduleParams.highChannel1[0].frequency = 10000000;  // 高段一本振1通道 10MHz
    moduleParams.highChannel1[1].frequency = 10000000;  // 高段二本振1通道 10MHz
    moduleParams.highChannel2[0].frequency = 12000000;  // 高段一本振2通道 12MHz
    moduleParams.highChannel2[1].frequency = 12000000;  // 高段二本振2通道 12MHz
    moduleParams.highChannel3[0].frequency = 14000000;  // 高段一本振3通道 14MHz
    moduleParams.highChannel3[1].frequency = 14000000;  // 高段二本振3通道 14MHz
    
    // 初始化BIT信息
    bitInfo.temperature = 25;  // 默认25℃
    for (int i = 0; i < 8; i++) {
        bitInfo.voltage5_5V[i] = 55;  // 5.5V
    }
    bitInfo.voltageNeg5V = 0xFF;    // 模块无负压，传递无效值
    bitInfo.lockStatus = 0xFFFF;  // 全部锁定
    bitInfo.refSignalStatus = 1;  // 参考信号正常
}

/**
  * @brief  更新BIT信息（从硬件读取）
  * @param  无
  * @retval 无
  */
void UpdateBitInfo(void)
{
    // 这里应该从实际硬件读取，现在使用模拟值
    // bitInfo.temperature = GetTempValue();  // 从温度传感器读取
    // 读取各通道电压等...
}

/**
  * @brief  处理产品信息查询
  * @param  MarkAddr -- MARK地址
  * @retval 无
  */
static void ProcessProductInfoQuery(uint8_t MarkAddr)
{
    memset(canTxBuffer, 0, sizeof(canTxBuffer));
    
    canTxBuffer[0] = MSG_CATEGORY_F0;
    canTxBuffer[1] = MSG_ID_PRODUCT_QUERY;
    canTxBuffer[2] = productInfo.manufacturerCode;
    canTxBuffer[3] = (productInfo.productYear >> 8) & 0xFF;   // 年千百位BCD
    canTxBuffer[4] = productInfo.productYear & 0xFF;          // 年十个位BCD
    canTxBuffer[5] = productInfo.productMonth;                 // 月BCD
    canTxBuffer[6] = productInfo.productDay;                   // 日BCD
    canTxBuffer[7] = (productInfo.serialYear >> 8) & 0xFF;    // 序列号年千百位BCD
    canTxBuffer[8] = productInfo.serialYear & 0xFF;           // 序列号年十个位BCD
    canTxBuffer[9] = productInfo.serialBatch;                  // 批次BCD
    canTxBuffer[10] = (productInfo.serialNum >> 8) & 0xFF;    // 序号百位BCD
    canTxBuffer[11] = productInfo.serialNum & 0xFF;           // 序号十个位BCD
    canTxBuffer[12] = MarkAddr;                                // MARK地址
    
    // 低段一本振工作频率范围 30000-3000000 kHz
    canTxBuffer[13] = 0x00;
    canTxBuffer[14] = 0x00;
    canTxBuffer[15] = 0x75;
    canTxBuffer[16] = 0x30;  // 30000
    
    canTxBuffer[17] = 0x00;
    canTxBuffer[18] = 0x2D;
    canTxBuffer[19] = 0xC6;
    canTxBuffer[20] = 0xC0;  // 3000000
    
    // 高段一本振工作频率范围 2000000-18000000 kHz
    canTxBuffer[21] = 0x00;
    canTxBuffer[22] = 0x1E;
    canTxBuffer[23] = 0x84;
    canTxBuffer[24] = 0x80;  // 2000000
    
    canTxBuffer[25] = 0x01;
    canTxBuffer[26] = 0x12;
    canTxBuffer[27] = 0xA8;
    canTxBuffer[28] = 0x80;  // 18000000
    
    // 高段二本振工作频率范围 2000000-18000000 kHz
    canTxBuffer[29] = 0x00;
    canTxBuffer[30] = 0x1E;
    canTxBuffer[31] = 0x84;
    canTxBuffer[32] = 0x80;  // 2000000
    
    canTxBuffer[33] = 0x01;
    canTxBuffer[34] = 0x12;
    canTxBuffer[35] = 0xA8;
    canTxBuffer[36] = 0x80;  // 18000000
    
    canTxLength = 37;
    SendCanResponse(MarkAddr);
}

/**
  * @brief  处理BIT查询
  * @param  MarkAddr -- MARK地址
  * @retval 无
  */
static void ProcessBitQuery(uint8_t MarkAddr)
{
    memset(canTxBuffer, 0, sizeof(canTxBuffer));
    
    canTxBuffer[0] = MSG_CATEGORY_F0;
    canTxBuffer[1] = MSG_ID_BIT_QUERY;
    canTxBuffer[2] = (uint8_t)bitInfo.temperature;  // 温度值（有符号）
    
    // 8个通道的+5.5V电压值
    for (int i = 0; i < 8; i++) {
        canTxBuffer[3 + i] = bitInfo.voltage5_5V[i];
    }
    
    // -5V电压值
    canTxBuffer[11] = bitInfo.voltageNeg5V;
    
    // 锁定状态位
    canTxBuffer[12] = bitInfo.lockStatus & 0xFF;
    canTxBuffer[13] = (bitInfo.lockStatus >> 8) & 0xFF;
    canTxBuffer[13] |= (bitInfo.refSignalStatus << 3);  // BIT3：低段二本振参考信号状态
    
    canTxLength = 14;
    SendCanResponse(MarkAddr);
}

/**
  * @brief  处理通道参数查询
  * @param  channelNum -- 通道编号
  * @param  MarkAddr -- MARK地址
  * @retval 无
  */
static void ProcessParamQuery(uint8_t channelNum, uint8_t MarkAddr)
{
    memset(canTxBuffer, 0, sizeof(canTxBuffer));
    
    canTxBuffer[0] = MSG_CATEGORY_F0;
    canTxBuffer[1] = MSG_ID_PARAM_QUERY;
    canTxBuffer[2] = channelNum;
    canTxBuffer[3] = bitInfo.refSignalStatus;  // 低段二本振参考输入信号状态
    
    uint32_t freq;
    
    switch (channelNum) {
        case CHANNEL_ALL:  // 全通道查询
            // 低段一本振1通道频率
            freq = moduleParams.lowChannel1.frequency;
            canTxBuffer[4] = (freq >> 24) & 0xFF;
            canTxBuffer[5] = (freq >> 16) & 0xFF;
            canTxBuffer[6] = (freq >> 8) & 0xFF;
            canTxBuffer[7] = freq & 0xFF;
            
            // 高段一本振1通道频率
            freq = moduleParams.highChannel1[0].frequency;
            canTxBuffer[8] = (freq >> 24) & 0xFF;
            canTxBuffer[9] = (freq >> 16) & 0xFF;
            canTxBuffer[10] = (freq >> 8) & 0xFF;
            canTxBuffer[11] = freq & 0xFF;
            
            // 高段二本振1通道频率
            freq = moduleParams.highChannel1[1].frequency;
            canTxBuffer[12] = (freq >> 24) & 0xFF;
            canTxBuffer[13] = (freq >> 16) & 0xFF;
            canTxBuffer[14] = (freq >> 8) & 0xFF;
            canTxBuffer[15] = freq & 0xFF;
            
            // 工作模式
            canTxBuffer[16] = moduleParams.lowSwitchMode & 0x03;
            
            // 额外的通道参数（全通道查询时）
            // 低段一本振2通道频率
            freq = moduleParams.lowChannel2.frequency;
            canTxBuffer[17] = (freq >> 24) & 0xFF;
            canTxBuffer[18] = (freq >> 16) & 0xFF;
            canTxBuffer[19] = (freq >> 8) & 0xFF;
            canTxBuffer[20] = freq & 0xFF;
            
            // 高段一本振2通道频率
            freq = moduleParams.highChannel2[0].frequency;
            canTxBuffer[21] = (freq >> 24) & 0xFF;
            canTxBuffer[22] = (freq >> 16) & 0xFF;
            canTxBuffer[23] = (freq >> 8) & 0xFF;
            canTxBuffer[24] = freq & 0xFF;
            
            // 高段二本振2通道频率
            freq = moduleParams.highChannel2[1].frequency;
            canTxBuffer[25] = (freq >> 24) & 0xFF;
            canTxBuffer[26] = (freq >> 16) & 0xFF;
            canTxBuffer[27] = (freq >> 8) & 0xFF;
            canTxBuffer[28] = freq & 0xFF;
            
            // 高段一本振3通道频率
            freq = moduleParams.highChannel3[0].frequency;
            canTxBuffer[29] = (freq >> 24) & 0xFF;
            canTxBuffer[30] = (freq >> 16) & 0xFF;
            canTxBuffer[31] = (freq >> 8) & 0xFF;
            canTxBuffer[32] = freq & 0xFF;
            
            // 高段二本振3通道频率
            freq = moduleParams.highChannel3[1].frequency;
            canTxBuffer[33] = (freq >> 24) & 0xFF;
            canTxBuffer[34] = (freq >> 16) & 0xFF;
            canTxBuffer[35] = (freq >> 8) & 0xFF;
            canTxBuffer[36] = freq & 0xFF;
            
            canTxLength = 37;
            break;
            
        case CHANNEL_LOW1:  // 低段一本振1通道
            freq = moduleParams.lowChannel1.frequency;
            canTxBuffer[4] = (freq >> 24) & 0xFF;
            canTxBuffer[5] = (freq >> 16) & 0xFF;
            canTxBuffer[6] = (freq >> 8) & 0xFF;
            canTxBuffer[7] = freq & 0xFF;
            
            // 高段频率无效
            memset(&canTxBuffer[8], 0xFF, 8);
            canTxBuffer[16] = moduleParams.lowChannel1.workMode & 0x03;
            canTxLength = 17;
            break;
            
        case CHANNEL_LOW2:  // 低段一本振2通道
            freq = moduleParams.lowChannel2.frequency;
            canTxBuffer[4] = (freq >> 24) & 0xFF;
            canTxBuffer[5] = (freq >> 16) & 0xFF;
            canTxBuffer[6] = (freq >> 8) & 0xFF;
            canTxBuffer[7] = freq & 0xFF;
            
            // 高段频率无效
            memset(&canTxBuffer[8], 0xFF, 8);
            canTxBuffer[16] = moduleParams.lowChannel2.workMode & 0x03;
            canTxLength = 17;
            break;
            
        case CHANNEL_LOW_SWITCH:  // 低段一本振开关/功分切换
            // 低段频率无效
            memset(&canTxBuffer[4], 0xFF, 12);
            canTxBuffer[16] = moduleParams.lowSwitchMode & 0x03;
            canTxLength = 17;
            break;
            
        case CHANNEL_HIGH1_1:  // 高段一本振1通道
        case CHANNEL_HIGH2_1:  // 高段二本振1通道
            // 低段频率无效
            memset(&canTxBuffer[4], 0xFF, 4);
            
            // 高段一本振1通道
            freq = moduleParams.highChannel1[0].frequency;
            canTxBuffer[8] = (freq >> 24) & 0xFF;
            canTxBuffer[9] = (freq >> 16) & 0xFF;
            canTxBuffer[10] = (freq >> 8) & 0xFF;
            canTxBuffer[11] = freq & 0xFF;
            
            // 高段二本振1通道
            freq = moduleParams.highChannel1[1].frequency;
            canTxBuffer[12] = (freq >> 24) & 0xFF;
            canTxBuffer[13] = (freq >> 16) & 0xFF;
            canTxBuffer[14] = (freq >> 8) & 0xFF;
            canTxBuffer[15] = freq & 0xFF;
            
            canTxBuffer[16] = 0x00;  // 工作模式
            canTxLength = 17;
            break;
            
        case CHANNEL_HIGH1_2:  // 高段一本振2通道
        case CHANNEL_HIGH2_2:  // 高段二本振2通道
            // 低段频率无效
            memset(&canTxBuffer[4], 0xFF, 4);
            
            // 高段一本振2通道
            freq = moduleParams.highChannel2[0].frequency;
            canTxBuffer[8] = (freq >> 24) & 0xFF;
            canTxBuffer[9] = (freq >> 16) & 0xFF;
            canTxBuffer[10] = (freq >> 8) & 0xFF;
            canTxBuffer[11] = freq & 0xFF;
            
            // 高段二本振2通道
            freq = moduleParams.highChannel2[1].frequency;
            canTxBuffer[12] = (freq >> 24) & 0xFF;
            canTxBuffer[13] = (freq >> 16) & 0xFF;
            canTxBuffer[14] = (freq >> 8) & 0xFF;
            canTxBuffer[15] = freq & 0xFF;
            
            canTxBuffer[16] = 0x00;  // 工作模式
            canTxLength = 17;
            break;
            
        case CHANNEL_HIGH1_3:  // 高段一本振3通道
        case CHANNEL_HIGH2_3:  // 高段二本振3通道
            // 低段频率无效
            memset(&canTxBuffer[4], 0xFF, 4);
            
            // 高段一本振3通道
            freq = moduleParams.highChannel3[0].frequency;
            canTxBuffer[8] = (freq >> 24) & 0xFF;
            canTxBuffer[9] = (freq >> 16) & 0xFF;
            canTxBuffer[10] = (freq >> 8) & 0xFF;
            canTxBuffer[11] = freq & 0xFF;
            
            // 高段二本振3通道
            freq = moduleParams.highChannel3[1].frequency;
            canTxBuffer[12] = (freq >> 24) & 0xFF;
            canTxBuffer[13] = (freq >> 16) & 0xFF;
            canTxBuffer[14] = (freq >> 8) & 0xFF;
            canTxBuffer[15] = freq & 0xFF;
            
            canTxBuffer[16] = 0x00;  // 工作模式
            canTxLength = 17;
            break;
            
        default:
            // 无效通道，不响应
            canTxLength = 0;
            return;
    }
    
    SendCanResponse(MarkAddr);
}

/**
  * @brief  处理通道参数设置
  * @param  CanRxData -- 接收数据
  * @param  MarkAddr -- MARK地址
  * @retval 无
  */
static void ProcessParamSet(uint8_t CanRxData[], uint8_t MarkAddr)
{
    uint8_t channelNum = CanRxData[2];
    uint8_t workMode = CanRxData[3] & 0x03;
    uint32_t lowFreq = (CanRxData[4] << 24) | (CanRxData[5] << 16) | 
                       (CanRxData[6] << 8) | CanRxData[7];
    uint32_t high1Freq = (CanRxData[8] << 24) | (CanRxData[9] << 16) | 
                         (CanRxData[10] << 8) | CanRxData[11];
    uint32_t high2Freq = (CanRxData[12] << 24) | (CanRxData[13] << 16) | 
                         (CanRxData[14] << 8) | CanRxData[15];
    
    // 清空FPGA命令缓冲区
    memset(fpgaCommandBuffer, 0, sizeof(fpgaCommandBuffer));
    fpgaCommandLength = 0;
    
    switch (channelNum) {
        case CHANNEL_ALL:  // 全通道设置
            if (lowFreq >= 30000 && lowFreq <= 3000000) {
                moduleParams.lowChannel1.frequency = lowFreq;
                moduleParams.lowChannel2.frequency = lowFreq;
                moduleParams.lowChannel1.workMode = workMode;
                moduleParams.lowChannel2.workMode = workMode;
                moduleParams.lowSwitchMode = workMode;
                
                // 生成FPGA命令（示例）
                fpgaCommandBuffer[fpgaCommandLength++] = 0x01;  // 设置低段频率命令
                fpgaCommandBuffer[fpgaCommandLength++] = (lowFreq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (lowFreq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (lowFreq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = lowFreq & 0xFF;
            }
            
            if (high1Freq >= 2000000 && high1Freq <= 18000000) {
                moduleParams.highChannel1[0].frequency = high1Freq;
                moduleParams.highChannel2[0].frequency = high1Freq;
                moduleParams.highChannel3[0].frequency = high1Freq;
                
                // 生成FPGA命令（示例）
                fpgaCommandBuffer[fpgaCommandLength++] = 0x02;  // 设置高段一频率命令
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = high1Freq & 0xFF;
            }
            
            if (high2Freq >= 2000000 && high2Freq <= 18000000) {
                moduleParams.highChannel1[1].frequency = high2Freq;
                moduleParams.highChannel2[1].frequency = high2Freq;
                moduleParams.highChannel3[1].frequency = high2Freq;
                
                // 生成FPGA命令（示例）
                fpgaCommandBuffer[fpgaCommandLength++] = 0x03;  // 设置高段二频率命令
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = high2Freq & 0xFF;
            }
            break;
            
        case CHANNEL_LOW1:  // 低段一本振1通道
            if (lowFreq >= 30000 && lowFreq <= 3000000) {
                moduleParams.lowChannel1.frequency = lowFreq;
                moduleParams.lowChannel1.workMode = workMode;
                
                // 生成FPGA命令
                fpgaCommandBuffer[fpgaCommandLength++] = 0x11;  // 设置低段1通道命令
                fpgaCommandBuffer[fpgaCommandLength++] = (lowFreq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (lowFreq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (lowFreq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = lowFreq & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = workMode;
            }
            break;
            
        case CHANNEL_LOW2:  // 低段一本振2通道
            if (lowFreq >= 30000 && lowFreq <= 3000000) {
                moduleParams.lowChannel2.frequency = lowFreq;
                moduleParams.lowChannel2.workMode = workMode;
                
                // 生成FPGA命令
                fpgaCommandBuffer[fpgaCommandLength++] = 0x12;  // 设置低段2通道命令
                fpgaCommandBuffer[fpgaCommandLength++] = (lowFreq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (lowFreq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (lowFreq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = lowFreq & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = workMode;
            }
            break;
            
        case CHANNEL_LOW_SWITCH:  // 低段一本振开关/功分切换
            moduleParams.lowSwitchMode = workMode;
            
            // 生成FPGA命令
            fpgaCommandBuffer[fpgaCommandLength++] = 0x13;  // 设置开关模式命令
            fpgaCommandBuffer[fpgaCommandLength++] = workMode;
            break;
            
        case CHANNEL_HIGH1_1:  // 高段一本振1通道
            if (high1Freq >= 2000000 && high1Freq <= 18000000) {
                moduleParams.highChannel1[0].frequency = high1Freq;
                
                // 生成FPGA命令
                fpgaCommandBuffer[fpgaCommandLength++] = 0x21;  // 设置高段一1通道命令
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = high1Freq & 0xFF;
            }
            break;
            
        case CHANNEL_HIGH2_1:  // 高段二本振1通道
            if (high2Freq >= 2000000 && high2Freq <= 18000000) {
                moduleParams.highChannel1[1].frequency = high2Freq;
                
                // 生成FPGA命令
                fpgaCommandBuffer[fpgaCommandLength++] = 0x31;  // 设置高段二1通道命令
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = high2Freq & 0xFF;
            }
            break;
            
        case CHANNEL_HIGH1_2:  // 高段一本振2通道
            if (high1Freq >= 2000000 && high1Freq <= 18000000) {
                moduleParams.highChannel2[0].frequency = high1Freq;
                
                // 生成FPGA命令
                fpgaCommandBuffer[fpgaCommandLength++] = 0x22;  // 设置高段一2通道命令
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = high1Freq & 0xFF;
            }
            break;
            
        case CHANNEL_HIGH2_2:  // 高段二本振2通道
            if (high2Freq >= 2000000 && high2Freq <= 18000000) {
                moduleParams.highChannel2[1].frequency = high2Freq;
                
                // 生成FPGA命令
                fpgaCommandBuffer[fpgaCommandLength++] = 0x32;  // 设置高段二2通道命令
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = high2Freq & 0xFF;
            }
            break;
            
        case CHANNEL_HIGH1_3:  // 高段一本振3通道
            if (high1Freq >= 2000000 && high1Freq <= 18000000) {
                moduleParams.highChannel3[0].frequency = high1Freq;
                
                // 生成FPGA命令
                fpgaCommandBuffer[fpgaCommandLength++] = 0x23;  // 设置高段一3通道命令
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high1Freq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = high1Freq & 0xFF;
            }
            break;
            
        case CHANNEL_HIGH2_3:  // 高段二本振3通道
            if (high2Freq >= 2000000 && high2Freq <= 18000000) {
                moduleParams.highChannel3[1].frequency = high2Freq;
                
                // 生成FPGA命令
                fpgaCommandBuffer[fpgaCommandLength++] = 0x33;  // 设置高段二3通道命令
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 24) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 16) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = (high2Freq >> 8) & 0xFF;
                fpgaCommandBuffer[fpgaCommandLength++] = high2Freq & 0xFF;
            }
            break;
            
        default:
            // 无效通道，不处理
            return;
    }
    
    // 这里应该将参数保存到Flash
    // Stm32Flash_Write(...);
    
    // 这里应该通过SPI将命令发送给FPGA
    // SPI_SendData(fpgaCommandBuffer, fpgaCommandLength);
}

/**
  * @brief  发送CAN响应
  * @param  MarkAddr -- MARK地址
  * @retval 无
  */
static void SendCanResponse(uint8_t MarkAddr)
{
    if (canTxLength > 0) {
        // 实际发送CAN数据
        // 使用查询应答数据的优先级(DATA_INQUIRE = 0x04)
        CANxSend(canTxBuffer, canTxLength, DATA_INQUIRE, MarkAddr);
        
        // 清空发送缓冲区长度，为下次发送做准备
        canTxLength = 0;
    }
}

/**
  * @brief  CAN接收数据处理
  * @param  CanRxData -- 接收数组  
  * @param  CanDataLength -- 数据长度
  * @param  MarkAddr -- MARK地址
  * @retval 无
  */
void CanRxDataProcess(uint8_t CanRxData[], uint16_t CanDataLength, uint8_t MarkAddr)
{
    // 检查数据长度
    if (CanDataLength < 2) {
        return;
    }
    
    // 检查消息类别
    if (CanRxData[0] != MSG_CATEGORY_F0) {
        return;
    }
    
    // 根据消息ID处理
    switch (CanRxData[1]) {
        case MSG_ID_PRODUCT_QUERY:  // 产品信息查询
            ProcessProductInfoQuery(MarkAddr);
            break;
            
        case MSG_ID_BIT_QUERY:      // BIT查询
            UpdateBitInfo();  // 先更新BIT信息
            ProcessBitQuery(MarkAddr);
            break;
            
        case MSG_ID_PARAM_QUERY:    // 通道参数查询
            if (CanDataLength >= 3) {
                ProcessParamQuery(CanRxData[2], MarkAddr);
            }
            break;
            
        case MSG_ID_PARAM_SET:      // 通道参数设置
            if (CanDataLength >= 16) {
                ProcessParamSet(CanRxData, MarkAddr);
            }
            break;
            
        case 0x0E:  // 调试用-产品信息设置 (自定义)
            // 数据格式：
            // BYTE2: 厂家代号
            // BYTE3-4: 出厂年份 (BCD)
            // BYTE5: 出厂月份 (BCD)
            // BYTE6: 出厂日期 (BCD)
            // BYTE7-8: 序列号年份 (BCD)
            // BYTE9: 序列号批次 (BCD)
            // BYTE10-11: 序列号序号 (BCD)
            if (CanDataLength >= 12) {
                productInfo.manufacturerCode = CanRxData[2];
                productInfo.productYear = (CanRxData[3] << 8) | CanRxData[4];
                productInfo.productMonth = CanRxData[5];
                productInfo.productDay = CanRxData[6];
                productInfo.serialYear = (CanRxData[7] << 8) | CanRxData[8];
                productInfo.serialBatch = CanRxData[9];
                productInfo.serialNum = (CanRxData[10] << 8) | CanRxData[11];
                
                // 保存到Flash
                SetProductInfo();
                
                // 响应确认
                canTxBuffer[0] = 0xF0;
                canTxBuffer[1] = 0x0E;
                canTxBuffer[2] = 0x00;  // 成功
                canTxLength = 3;
                SendCanResponse(MarkAddr);
            }
            break;
            
        default:
            // 未知消息ID，不处理
            break;
    }
}

/**
  * @brief  保存产品信息到Flash
  * @param  无
  * @retval 无
  */
static void SetProductInfo(void)
{
    uint32_t productInfoData[3];
    
    // 将产品信息打包成3个32位字
    productInfoData[0] = (productInfo.productYear << 16) | 
                        (productInfo.productMonth << 8) | 
                        productInfo.productDay;
    
    productInfoData[1] = (productInfo.serialYear << 16) | 
                        (productInfo.serialBatch << 8) | 
                        ((productInfo.serialNum >> 8) & 0xFF);
    
    productInfoData[2] = (productInfo.serialNum & 0xFF) << 8;
    
    // 写入Flash
    STMFLASH_Write(STORE_ADDR, productInfoData, 3);
}

/*********************************************END OF FILE**********************/
