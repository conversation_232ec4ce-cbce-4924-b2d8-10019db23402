#ifndef _CANDATAPROCESS_H
#define _CANDATAPROCESS_H

/* 头文件包含 ----------------------------------------------------------------*/
#include "stm32f1xx_hal.h"  

/* 宏定义 --------------------------------------------------------------------*/

/* 结构体定义 ----------------------------------------------------------------*/
// 低段一本振通道参数结构体
typedef struct {
    uint32_t frequency;       // 频率 (kHz) [30000-3000000]
    uint8_t workMode;         // 工作模式 0:功分 1:开关 2:低功耗
} LowChannel_t;

// 高段本振通道参数结构体
typedef struct {
    uint32_t frequency;       // 频率 (kHz) [2000000-18000000]
} HighChannel_t;

// 模块参数结构体
typedef struct {
    LowChannel_t lowChannel1;     // 低段一本振1通道
    LowChannel_t lowChannel2;     // 低段一本振2通道
    uint8_t lowSwitchMode;        // 低段一本振开关/功分切换 0:开关 1:功分 2:低功耗
    HighChannel_t highChannel1[2]; // 高段一/二本振1通道 [0]:高段一 [1]:高段二
    HighChannel_t highChannel2[2]; // 高段一/二本振2通道 [0]:高段一 [1]:高段二
    HighChannel_t highChannel3[2]; // 高段一/二本振3通道 [0]:高段一 [1]:高段二
} ModuleParams_t;

// 产品信息结构体
typedef struct {
    uint8_t manufacturerCode;   // 厂商代号
    uint16_t productYear;       // 出厂年份 BCD
    uint8_t productMonth;       // 出厂月份 BCD
    uint8_t productDay;         // 出厂日期 BCD
    uint16_t serialYear;        // 序列号年份 BCD
    uint8_t serialBatch;        // 序列号批次 BCD
    uint16_t serialNum;         // 序列号 BCD
} ProductInfo_t;

// BIT信息结构体
typedef struct {
    int8_t temperature;         // 温度值 [-55 ~ 75]℃
    uint8_t voltage5_5V[8];     // 8个通道的+5.5V电压值 0.1V精度
    uint8_t voltageNeg5V;       // -5V电压值 0.1V精度
    uint16_t lockStatus;        // 锁定状态位
    uint8_t refSignalStatus;    // 参考信号状态
} BitInfo_t;

/* 全局变量声明 --------------------------------------------------------------*/
extern ModuleParams_t moduleParams;              // 模块参数
extern ProductInfo_t productInfo;                // 产品信息
extern BitInfo_t bitInfo;                        // BIT信息

// 用于替代实际下发和上报的变量
extern uint8_t canTxBuffer[];                    // CAN发送缓冲区
extern uint16_t canTxLength;                     // CAN发送长度
extern uint8_t fpgaCommandBuffer[];              // FPGA命令缓冲区
extern uint8_t fpgaCommandLength;                // FPGA命令长度

/* 函数声明 ------------------------------------------------------------------*/ 
void CanRxDataProcess(uint8_t CanRxData[], uint16_t CanDataLength, uint8_t MarkAddr);
void InitModuleParams(void);
void UpdateBitInfo(void);

#endif 

/*********************************************END OF FILE**********************/

