{"configurations": [{"name": "STM32", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/Libraries/STM32F1xx_HAL_Driver/Inc", "${workspaceFolder}/Libraries/CMSIS//Device/ST/STM32F1xx/Include", "${workspaceFolder}/Libraries/CMSIS/Include"], "defines": ["USE_HAL_DRIVER", "STM32F107xC"], "compilerPath": "D:/Program_Tools/GNU Arm Embedded Toolchain/10 2021.10/bin/arm-none-eabi-gcc.exe", "cStandard": "c11", "cppStandard": "c++14", "intelliSenseMode": "gcc-arm", "browse": {"path": ["${workspaceFolder}/**"], "limitSymbolsToIncludedHeaders": true}}], "version": 4}