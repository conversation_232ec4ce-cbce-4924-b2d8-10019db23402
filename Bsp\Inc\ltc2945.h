#ifndef __LTC2945_H
#define __LTC2945_H

#include "stm32f1xx_hal.h"

// LTC2945 I2C address
#define LTC2945_I2C_ADDR_WRITE    0xCC  // Write address (8-bit) - 与C51代码保持一致
#define LTC2945_I2C_ADDR_READ     0xCF  // Read address (8-bit) - 与C51代码保持一致
#define LTC2945_I2C_ADDR_7BIT     0x66  // 7-bit address (0xCC >> 1)

// LTC2945 Register addresses
#define LTC2945_REG_CONTROL_A     0x00
#define LTC2945_REG_CONTROL_B     0x01
#define LTC2945_REG_ALERT         0x02
#define LTC2945_REG_STATUS        0x02
#define LTC2945_REG_FAULT         0x03
#define LTC2945_REG_POWER_MSB2    0x05
#define LTC2945_REG_POWER_MSB1    0x06
#define LTC2945_REG_POWER_LSB     0x07
#define LTC2945_REG_MAX_POWER_MSB2    0x08
#define LTC2945_REG_MAX_POWER_MSB1    0x09
#define LTC2945_REG_MAX_POWER_LSB     0x0A
#define LTC2945_REG_MIN_POWER_MSB2    0x0B
#define LTC2945_REG_MIN_POWER_MSB1    0x0C
#define LTC2945_REG_MIN_POWER_LSB     0x0D
#define LTC2945_REG_MAX_POWER_THRESHOLD_MSB2  0x0E
#define LTC2945_REG_MAX_POWER_THRESHOLD_MSB1  0x0F
#define LTC2945_REG_MAX_POWER_THRESHOLD_LSB   0x10
#define LTC2945_REG_MIN_POWER_THRESHOLD_MSB2  0x11
#define LTC2945_REG_MIN_POWER_THRESHOLD_MSB1  0x12
#define LTC2945_REG_MIN_POWER_THRESHOLD_LSB   0x13
#define LTC2945_REG_SENSE_MSB     0x14
#define LTC2945_REG_SENSE_LSB     0x15
#define LTC2945_REG_MAX_SENSE_MSB 0x16
#define LTC2945_REG_MAX_SENSE_LSB 0x17
#define LTC2945_REG_MIN_SENSE_MSB 0x18
#define LTC2945_REG_MIN_SENSE_LSB 0x19
#define LTC2945_REG_MAX_SENSE_THRESHOLD_MSB   0x1A
#define LTC2945_REG_MAX_SENSE_THRESHOLD_LSB   0x1B
#define LTC2945_REG_MIN_SENSE_THRESHOLD_MSB   0x1C
#define LTC2945_REG_MIN_SENSE_THRESHOLD_LSB   0x1D
#define LTC2945_REG_VIN_MSB       0x1E
#define LTC2945_REG_VIN_LSB       0x1F
#define LTC2945_REG_MAX_VIN_MSB   0x20
#define LTC2945_REG_MAX_VIN_LSB   0x21
#define LTC2945_REG_MIN_VIN_MSB   0x22
#define LTC2945_REG_MIN_VIN_LSB   0x23
#define LTC2945_REG_MAX_VIN_THRESHOLD_MSB     0x24
#define LTC2945_REG_MAX_VIN_THRESHOLD_LSB     0x25
#define LTC2945_REG_MIN_VIN_THRESHOLD_MSB     0x26
#define LTC2945_REG_MIN_VIN_THRESHOLD_LSB     0x27
#define LTC2945_REG_ADIN_MSB      0x28
#define LTC2945_REG_ADIN_LSB      0x29
#define LTC2945_REG_MAX_ADIN_MSB  0x2A
#define LTC2945_REG_MAX_ADIN_LSB  0x2B
#define LTC2945_REG_MIN_ADIN_MSB  0x2C
#define LTC2945_REG_MIN_ADIN_LSB  0x2D
#define LTC2945_REG_MAX_ADIN_THRESHOLD_MSB    0x2E
#define LTC2945_REG_MAX_ADIN_THRESHOLD_LSB    0x2F
#define LTC2945_REG_MIN_ADIN_THRESHOLD_MSB    0x30
#define LTC2945_REG_MIN_ADIN_THRESHOLD_LSB    0x31

// Control register bit definitions
#define LTC2945_CTRL_A_MEASURE_ALL    0x00
#define LTC2945_CTRL_A_SHUTDOWN       0x01

// Timeout for I2C operations (ms)
#define LTC2945_I2C_TIMEOUT       100

// Structure for LTC2945 measurements
typedef struct {
    uint16_t sense_voltage;       // Current sense voltage (registers 0x14-0x15)
    uint32_t power;              // Power measurement (registers 0x05-0x07)
    uint16_t vin_voltage;        // Input voltage (registers 0x1E-0x1F)
    uint16_t adin_voltage;       // ADIN voltage (registers 0x28-0x29)
} LTC2945_Data_t;

// Structure for LTC2945 handle
typedef struct {
    uint16_t device_address;     // Device I2C address (7-bit)
} LTC2945_Handle_t;

// Function prototypes
HAL_StatusTypeDef LTC2945_Init(LTC2945_Handle_t *hltc2945);
HAL_StatusTypeDef LTC2945_ReadRegister(LTC2945_Handle_t *hltc2945, uint8_t reg_addr, uint8_t *data);
HAL_StatusTypeDef LTC2945_WriteRegister(LTC2945_Handle_t *hltc2945, uint8_t reg_addr, uint8_t data);
HAL_StatusTypeDef LTC2945_WriteControlRegisters(LTC2945_Handle_t *hltc2945, uint8_t ctrl_a, uint8_t ctrl_b);
HAL_StatusTypeDef LTC2945_WriteCurrentThreshold(LTC2945_Handle_t *hltc2945, uint16_t max_threshold, uint16_t min_threshold);
HAL_StatusTypeDef LTC2945_ReadSenseVoltage(LTC2945_Handle_t *hltc2945, uint16_t *sense_voltage);
HAL_StatusTypeDef LTC2945_ReadPower(LTC2945_Handle_t *hltc2945, uint32_t *power);
HAL_StatusTypeDef LTC2945_ReadVinVoltage(LTC2945_Handle_t *hltc2945, uint16_t *vin_voltage);
HAL_StatusTypeDef LTC2945_ReadAdinVoltage(LTC2945_Handle_t *hltc2945, uint16_t *adin_voltage);
HAL_StatusTypeDef LTC2945_ReadAllData(LTC2945_Handle_t *hltc2945, LTC2945_Data_t *data);

// Current monitoring functions
float LTC2945_ConvertSenseToAmps(uint16_t sense_value, float sense_resistor_ohms);
float LTC2945_ConvertPowerToWatts(uint32_t power_value, float sense_resistor_ohms);
float LTC2945_ConvertVinToVolts(uint16_t vin_value);
float LTC2945_ConvertAdinToVolts(uint16_t adin_value);

#endif /* __LTC2945_H */
