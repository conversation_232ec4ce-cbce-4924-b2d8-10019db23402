/**
  ******************************************************************************
  * @file       : ADC.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : STM32F4内部ADC驱动 -- 单通道转换
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */

/* 头文件包含 ----------------------------------------------------------------*/
#include "adc.h"
#include "delay.h"

/* 变量定义 -------------------------------------------------------------------*/
ADC_HandleTypeDef hadc1;	// ADC句柄

/**
  * @brief  ADC初始化
  * @param  无
  * @retval 无
  */
void ADC_Init(void)
{
	RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};
	
	PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_ADC;
	PeriphClkInit.AdcClockSelection = RCC_ADCPCLK2_DIV6;
	HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit);
	
	hadc1.Instance  = ADC1;
    hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;             			// 右对齐
    hadc1.Init.ScanConvMode = DISABLE;                      			// 非扫描模式
    hadc1.Init.ContinuousConvMode = DISABLE;                			// 关闭连续转换
    hadc1.Init.NbrOfConversion = 1;                         			// 1个转换在规则序列中 也就是只转换规则序列1 
    hadc1.Init.DiscontinuousConvMode = DISABLE;             			// 禁止不连续采样模式
    hadc1.Init.NbrOfDiscConversion = 0;                     			// 不连续采样通道数为0
    hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;      	 			// 软件触发
    HAL_ADC_Init(&hadc1);                                 				// 初始化 
	
	HAL_ADCEx_Calibration_Start(&hadc1);					 			// 校准ADC
}

/**
  * @brief  ADC底层初始化函数
  * @param  adcHandle : ADC句柄
  * @retval 无
  * @note   此函数会被HAL_ADC_Init()调用
  */
void HAL_ADC_MspInit(ADC_HandleTypeDef* adcHandle)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
	
	if(adcHandle->Instance == ADC1)
	{
		__HAL_RCC_GPIOB_CLK_ENABLE();
		__HAL_RCC_ADC1_CLK_ENABLE();
		
		/**ADC1 GPIO 配置
		PB1     ------> ADC1_IN9
		*/
		GPIO_InitStruct.Pin = GPIO_PIN_1;
		GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
		HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
	}
}

/**
  * @brief  ADC转换
  * @param  ch(转换通道)
  * @retval 转换结果
  */
uint16_t Get_Adc(uint32_t ch)   
{
	ADC_ChannelConfTypeDef sConfig = {0};
    
    sConfig.Channel = ch;                              		// 通道
    sConfig.Rank = ADC_REGULAR_RANK_1;                  	// 第1个序列，序列1
    sConfig.SamplingTime = ADC_SAMPLETIME_28CYCLES_5; 		// 采样时间                
    HAL_ADC_ConfigChannel(&hadc1, &sConfig);        		// 通道配置
	
    HAL_ADC_Start(&hadc1);                               	// 开启ADC
	
    HAL_ADC_PollForConversion(&hadc1, 10);                	// 轮询转换
 
	return (uint16_t)HAL_ADC_GetValue(&hadc1);	        	// 返回最近一次ADC1规则组的转换结果
}

/**
  * @brief  获取指定通道的转换值，取times次,然后平均
  * @param  ch(转换通道)、times(获取次数)
  * @retval 转换结果平均值
  */
uint16_t Get_Adc_Average(uint32_t ch, uint8_t times)
{
	uint16_t temp_val = 0;
	uint8_t t;
	
	for(t = 0; t < times; t++)
	{
		temp_val += Get_Adc(ch);
		delay_us(1);
	}
	return temp_val / times;
} 

/**
  * @brief  获取内部温度传感器值
  * @param  无
  * @retval 温度值
  */
int8_t GetTempValeue(void)
{
	uint16_t AdcValue;
	float TempValue;
	
	AdcValue = Get_Adc_Average(ADC_CHANNEL_TEMPSENSOR, 5);	// 读取内部温度传感器，5次取平均
	TempValue = (float)AdcValue * 3.3 / 4096;				// 电压值
	TempValue = (1.43 - TempValue) / 0.0043 + 25;			// 转换为温度值 
	
	return (int8_t)TempValue;
}

/**
  * @brief  获取5.5V电压值
  * @param  无
  * @retval 5.5V电压值
  */
uint8_t GetVoltage5_5V(void)
{
	uint16_t AdcValue;
	float Temp;
	
	AdcValue = Get_Adc_Average(ADC_VOLTAGE, 5);	// 读取内部温度传感器，5次取平均
	Temp = (float)AdcValue * 3.3 / 4096;		// 电压值
	Temp = Temp * 3 * 10;
	
	return (uint8_t)Temp;
}

/*********************************************END OF FILE**********************/

