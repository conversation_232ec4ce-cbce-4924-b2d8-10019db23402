# FPGA控制模块更新说明

## 概述

根据通信协议-频综.md文档的要求，对fpga_ctrl模块进行了重新设计和实现，使其符合SPI通信协议规范。

## 主要更改

### 1. 通信协议更新

**原协议：**
- 自定义帧格式（0xAA开头，0x55结尾）
- 10字节固定长度
- 功能码方式控制

**新协议：**
- 符合通信协议-频综.md规范
- 大端模式，CKPOL=0, CKPHA=0，MSB优先
- 帧头0xF8，读写指令分离
- 18字节写操作，43字节读操作

### 2. 频率转换功能

**低段一本振频率转换：**
- CAN接收范围：[30000-3000000] kHz
- FPGA发送范围：[4000000-6970000] kHz
- 转换公式：FPGA频率 = CAN频率 + 3970000
- 转换映射：30M → 4000M，3000M → 6970M

**高段本振频率：**
- 高段一本振和高段二本振频率直接透传，不进行转换

### 3. 开关控制

**BYTE2~3开关控制：**
- 每次下发通道参数设置时都设置为开电状态
- BYTE2 = 0xFF（低8位全部开电）
- BYTE3 = 0x03（高2位开电）

### 4. 工作模式

**工作模式直接透传：**
- 从CAN数据中获取的工作模式直接发送给FPGA
- 0x00：低段一本振开关工作模式
- 0x01：低段一本振功分工作模式
- 0x02：低功耗工作模式

## 文件更改详情

### 1. Bsp/Src/fpga_ctrl.c

**完全重写，主要函数：**

```c
// 频率转换函数
static uint32_t ConvertLowFrequency(uint32_t canFreq);

// SPI通信函数
static void FPGA_SendFrame(uint8_t *txData, uint8_t *rxData, uint8_t length);

// 参数设置函数
void FPGA_SetChannelParams(uint8_t channelNum, uint8_t workMode, 
                          uint32_t lowFreq, uint32_t high1Freq, uint32_t high2Freq);

// 参数读取函数
void FPGA_ReadParams(void);
int8_t FPGA_GetTemperature(void);
uint16_t FPGA_GetLockStatus(void);

// CAN处理接口
void FPGA_ProcessChannelSet(uint8_t channelNum, uint8_t workMode, 
                           uint32_t lowFreq, uint32_t high1Freq, uint32_t high2Freq);
```

### 2. Bsp/Inc/fpga_ctrl.h

**更新定义：**

```c
// 频综模块信道编号定义
#define FPGA_CHANNEL_ALL        0x00    // 通道同时设置
#define FPGA_CHANNEL_LOW1       0x01    // 低段一本振1通道
#define FPGA_CHANNEL_LOW2       0x02    // 低段一本振2通道
// ... 其他通道定义

// 工作模式定义
#define FPGA_MODE_SWITCH        0x00    // 低段一本振开关工作模式
#define FPGA_MODE_POWER_DIV     0x01    // 低段一本振功分工作模式
#define FPGA_MODE_LOW_POWER     0x02    // 低功耗工作模式

// 频率范围定义
#define LOW_FREQ_MIN            30000       // 低段一本振最小频率 (kHz)
#define LOW_FREQ_MAX            3000000     // 低段一本振最大频率 (kHz)
#define HIGH_FREQ_MIN           2000000     // 高段本振最小频率 (kHz)
#define HIGH_FREQ_MAX           18000000    // 高段本振最大频率 (kHz)
```

### 3. Bsp/Src/CanDataProcess.c

**更新ProcessParamSet函数：**
- 移除了旧的FPGA命令缓冲区生成代码
- 添加了对新FPGA接口的调用：`FPGA_ProcessChannelSet()`

**更新UpdateBitInfo函数：**
- 添加了FPGA读取参数的调用：`FPGA_ReadParams()`
- 从FPGA获取温度和锁定状态信息

## SPI通信帧格式

### 写操作（参数设置）

| 字节 | 描述 | 值 |
|------|------|-----|
| BYTE0 | 帧头 | 0xF8 |
| BYTE1 | 读写指令 | 0x00（写操作） |
| BYTE2~3 | 开关控制 | 0xFF03（全部开电） |
| BYTE4 | 信道编号 | 0x01-0x09 |
| BYTE5 | 工作模式 | 0x00-0x02 |
| BYTE6~8 | 低段一本振频率（转换后） | 大端格式 |
| BYTE9~12 | 高段一本振频率（透传） | 大端格式 |
| BYTE13~16 | 高段二本振频率（透传） | 大端格式 |
| BYTE17 | 累加校验 | 0x00 |

### 读操作（参数查询）

| 字节 | 描述 |
|------|------|
| BYTE0 | 帧头 0xF8 |
| BYTE1 | 读写指令 0x01 |
| BYTE2 | 温度值 |
| BYTE3~4 | 锁定状态 |
| BYTE5~42 | 其他参数 |

## 使用说明

1. **参数设置：**
   ```c
   // 通过CAN接收到参数设置命令后，调用：
   FPGA_ProcessChannelSet(channelNum, workMode, lowFreq, high1Freq, high2Freq);
   ```

2. **状态查询：**
   ```c
   // BIT查询时，先读取FPGA参数：
   FPGA_ReadParams();
   int8_t temp = FPGA_GetTemperature();
   uint16_t lockStatus = FPGA_GetLockStatus();
   ```

3. **频率转换：**
   - 低段频率自动转换，无需手动处理
   - 高段频率直接透传

## 注意事项

1. **SPI接口：** 使用SPI1接口（PA4/NSS, PA5/SCK, PA6/MISO, PA7/MOSI）
2. **大端模式：** 所有多字节数据采用大端格式传输
3. **开关控制：** 每次参数设置都会发送开电命令
4. **频率范围：** 严格按照协议文档的频率范围进行验证
5. **工作模式：** 直接从CAN数据透传，不进行转换

## 兼容性

- 保持了CAN接口的兼容性
- 模块参数结构体保持不变
- 对外接口保持一致，内部实现完全重构
