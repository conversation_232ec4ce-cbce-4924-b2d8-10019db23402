# 通信协议-频综.md 更新总结

## 主要变化

### 1. 新增BYTE5
- **位置**: 在原BYTE4（信道编号）之后
- **功能**: LLO2开关控制
- **格式**: 
  - Bit0: LLO2_1开关 (0:关, 1:开)
  - Bit1: LLO2_2开关 (0:关, 1:开)
- **设置值**: 0x03 (两个开关都开电)

### 2. 字节顺序调整
原协议 → 新协议：
- BYTE5 (工作模式) → BYTE6 (工作模式)
- BYTE6~8 (低段频率) → BYTE7~10 (低段频率，改为32位)
- BYTE9~12 (高段一频率) → BYTE11~14 (高段一频率)
- BYTE13~16 (高段二频率) → BYTE15~18 (高段二频率)
- BYTE17 (校验) → BYTE19 (衰减)

### 3. 帧长度变化
- **写操作**: 18字节 → 20字节
- **读操作**: 43字节 → 5字节

### 4. 频率字段扩展
- **低段一本振频率**: 从24位(3字节)扩展为32位(4字节)
- **高段频率**: 保持32位(4字节)不变

## 代码更改

### fpga_ctrl.c 主要修改：

1. **缓冲区大小调整**:
   ```c
   uint8_t txBuffer[20];  // 18 → 20
   uint8_t rxBuffer[20];  // 18 → 20
   static uint8_t fpgaReadBuffer[5];  // 43 → 5
   ```

2. **新增BYTE5设置**:
   ```c
   // BYTE5: LLO2开关控制 - 都给1（开电状态）
   txBuffer[5] = 0x03;  // Bit0:LLO2_1开, Bit1:LLO2_2开
   ```

3. **字节位置调整**:
   ```c
   // BYTE6: 工作模式 (原BYTE5)
   txBuffer[6] = workMode & 0x03;
   
   // BYTE7~10: 低段频率 (原BYTE6~8，扩展为32位)
   txBuffer[7] = (convertedLowFreq >> 24) & 0xFF;
   txBuffer[8] = (convertedLowFreq >> 16) & 0xFF;
   txBuffer[9] = (convertedLowFreq >> 8) & 0xFF;
   txBuffer[10] = convertedLowFreq & 0xFF;
   
   // BYTE11~14: 高段一频率 (原BYTE9~12)
   // BYTE15~18: 高段二频率 (原BYTE13~16)
   // BYTE19: 衰减 (原BYTE17)
   ```

4. **读操作简化**:
   ```c
   // 读取帧长度从43字节减少到5字节
   FPGA_SendFrame(txBuffer, fpgaReadBuffer, 5);
   ```

## 协议兼容性

✅ **保持兼容的部分**:
- 帧头 (0xF8)
- 读写指令格式
- BYTE2~3开关控制
- BYTE4信道编号
- 工作模式定义
- 频率转换逻辑
- 温度和锁定状态读取

✅ **新增功能**:
- LLO2开关独立控制
- 低段频率32位精度
- 简化的读取操作

## 测试建议

1. **参数设置测试**: 验证20字节写操作帧格式
2. **开关控制测试**: 确认BYTE5的LLO2开关控制生效
3. **频率精度测试**: 验证32位低段频率设置
4. **读取操作测试**: 确认5字节读取响应正确
5. **兼容性测试**: 确保CAN接口功能不受影响

## 注意事项

⚠️ **重要提醒**:
- 新协议帧长度发生变化，需要确保FPGA端同步更新
- BYTE5的LLO2开关控制是新增功能，需要验证硬件响应
- 低段频率扩展为32位，提高了频率设置精度
- 读取操作大幅简化，提高了通信效率
