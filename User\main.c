/**
  ******************************************************************************
  * @file       : main.c
  * <AUTHOR> yechen
  * @version	: V1.0.0
  * @brief      : 程序主体
  ******************************************************************************
  * @attention
  *
  * None
  *
  ******************************************************************************
  */

/* 头文件包含 ----------------------------------------------------------------*/
#include "main.h"

/* 变量声明 -------------------------------------------------------------------*/
extern uint8_t Can1RxData[256];	// CAN接收数据
extern uint8_t Can2RxData[256];	// CAN接收数据
extern uint16_t Can1RxCpt;		// CAN1数据接收完成
extern uint16_t Can2RxCpt;		// CAN1数据接收完成

/* 变量定义 -------------------------------------------------------------------*/
uint16_t CanDataLength = 2;
uint8_t CanRxData[256], i, MarkAddr;

uint8_t FpgaAData[6], FpgaBData[6];

/**
  * @brief  主函数
  * @param  无
  * @retval 无
  */
int main(void)
{
	HAL_Init();					// HAL库初始化
	SystemClock_Config();		// 初始化时钟为72MHz
	delay_init(72);				// 延时初始化
	
	// 初始化模块参数
	InitModuleParams();			// 初始化CAN数据处理模块参数
	
	// 外设初始化
	ADC_Init();					// ADC1 
	TIM2_Init(7200 - 1, 5000 - 1);	// 定时器初始化
	LED_Init();					// LED IO初始化
	SPI3_Init();				// SPI3 -- 对应 FPGA
	
	// 延时100ms，等待电源稳定
	delay_ms(100);
	
	// CPLD初始化
	CanRxData[0] = 0xAA;
	CanRxData[1] = 0x55;
	CanRxDataProcess(CanRxData, CanDataLength, 0x00);
	
	// 延时1500ms，等待MARK地址稳定
	delay_ms(1500);				
	
	// 获取MARK地址
	MarkAddr = GetMarkAddr();	
	
	// CAN初始化
	CAN1_Init(0x00);
#ifndef DEV_BOARD_MODE
	// 开发板模式下不初始化CAN2
	CAN2_Init(0x00);
#endif

	while (1)
	{
		if ((Can1RxCpt&0x8000) || (Can2RxCpt&0x8000))	
		{
			// 数据缓存一次
			if (Can1RxCpt&0x8000)
			{
				CanDataLength = Can1RxCpt&0x7FFF;
				Can1RxCpt = 0x0000;
				
				for (i = 0; i < CanDataLength; i++)
				{
					CanRxData[i] = Can1RxData[i];
				}
			}
			else
			{
				CanDataLength = Can2RxCpt&0x7FFF;
				Can2RxCpt = 0x0000;
				
				for (i = 0; i < CanDataLength; i++)
				{
					CanRxData[i] = Can2RxData[i];
				}
			}
			
			// 数据处理
			CanRxDataProcess(CanRxData, CanDataLength, MarkAddr);
		}
		
		// 1S一次LED闪烁
		if (Time_1S)
		{
			Time_1S = 0;
			LED = ~LED;
		}
	}
}


/*********************************************END OF FILE**********************/

