#ifndef _CAN_H
#define _CAN_H

/* 头文件包含 ----------------------------------------------------------------*/
#include "stm32f1xx_hal.h"  
#include "board_config.h"

/* 宏定义 --------------------------------------------------------------------*/
// 调试模式
#define _DEBUG							//正常模式请注释关闭

// 数据优先级
#define DATA_START_STOP_COMMAND	0x01	// 启止命令
#define DATA_CONTROL_COMMAND	0x02	// 控制命令及参数设置
#define DATA_RADIATE			0x03	// 广播
#define DATA_INQUIRE			0x04	// 遥测/状态查询应答数据
// 平台类型
#define PLATFORM_CPCI			0x00	// CPCI
#define PLATFORM_VPX			0x01	// VPX
#define PLATFORM_LRM			0x02	// LRM
#define PLATFORM_OTHER			0x03	// 其他
// 本模块地址
#define SADDR					0x02
// 目的节点地址 -- 向本模块地址,转化成应答源节点地址					

/* 全局变量声明 --------------------------------------------------------------*/
typedef struct
{
    uint8_t DataType;			// 数据类型
	uint8_t PlatformType;		// 平台类型
	uint8_t CanChoose;			// CAN选择
	uint16_t SourceAddr;		// 源节点地址
	uint16_t DestinationAddr;	// 目的节点地址
} CAN_IdTypeDef;

/* 函数声明 ------------------------------------------------------------------*/ 
void CAN1_Init(uint16_t RxID);
void CAN2_Init(uint16_t RxID);
void CANxSend(uint8_t SendData[], uint8_t SendLength, uint8_t Priority, uint8_t MarkAddr);

#endif 

/*********************************************END OF FILE**********************/
